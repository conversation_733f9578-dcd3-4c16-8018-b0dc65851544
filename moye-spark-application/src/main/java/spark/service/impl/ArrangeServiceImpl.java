package spark.service.impl;

import com.trs.moye.ability.entity.operator.BatchOperatorExecutionRecord;
import com.trs.moye.ability.entity.operator.BatchOperatorRuntimeEntity;
import com.trs.moye.base.common.utils.JsonUtils;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.sql.Column;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.types.BinaryType;
import org.apache.spark.sql.types.DataTypes;
import org.apache.spark.sql.types.StructField;
import org.apache.spark.sql.types.StructType;
import spark.ability.base.Ability;
import spark.ability.base.AbilityFactory;
import spark.ability.base.AbilityInterface;
import spark.entity.TaskInfo;
import spark.exception.FailedOperatorException;
import spark.redis.JedisConnection;
import spark.service.ArrangeService;

/**
 * 算子编排执行
 *
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
public class ArrangeServiceImpl implements ArrangeService {

    @Override
    public List<BatchOperatorRuntimeEntity> getArrangementsFromRedis(SparkSession sparkSession, String taskId) {
        List<BatchOperatorRuntimeEntity> executeInfos = getExecuteInfoFromRedis(taskId);
        //校验算子是否合法，有问题则报错
        for (BatchOperatorRuntimeEntity job : executeInfos) {
            AbilityInterface ability = AbilityFactory.getAbility(job);
            if (ability != null) {
                ability.validate(job);
            }
        }
        return executeInfos;
    }

    @Override
    public void executeArrangement(SparkSession sparkSession, List<BatchOperatorRuntimeEntity> executeList, TaskInfo taskInfo) {

        // 算子执行前数据量
        long preCount;
        // 算子执行后数据量
        long count;

        // 遍历算子
        for (BatchOperatorRuntimeEntity job : executeList) {
            // 获取算子
            AbilityInterface ability = AbilityFactory.getAbility(job);

            if (ability != null) {
                preCount = 0;
                count = 0;
                // 执行算子, 记录执行情况
                Exception exception = null;
                List<String> fields = null;
                List<Map<String, Object>> resultDfSampleData = null;
                LocalDateTime startTime = LocalDateTime.now();

                try {
                    // 获取算子执行前数据量
                    if (job.getInputTables() != null && job.getInputTables().size() > 0) {
                        preCount = sparkSession.table(job.getInputTables().values().stream().iterator().next()).count();
                    }

                    Dataset<Row> resultDf = ability.process(sparkSession, job, taskInfo);
                    // 根据输出字段定义转型
                    resultDf = Ability.convertDF(resultDf, job.getOutputFields());

                    count = resultDf.count();
                    Ability.save(resultDf, job, taskInfo.getBatchId(), count);

                    // 记录字段和示例数据, 用于展示
                    fields = Arrays.asList(resultDf.columns());
                    resultDfSampleData = convertToStructuredData(resultDf);
                } catch (Exception e) {
                    exception = e;
                    String errorMsg = String.format("执行算子[outputTableName:%s]发生异常", job.getOutputTableName());
                    log.error(errorMsg, e);
                    throw new FailedOperatorException(errorMsg, e);
                } finally {
                    saveOperatorExecutionRecordToRedis(taskInfo.getBatchId(),
                        BatchOperatorExecutionRecord.fromBatchOperator(job, fields, resultDfSampleData, preCount, count, exception,
                            startTime, LocalDateTime.now()));
                }
            }
        }
    }

    /**
     * 从redis获取执行信息
     *
     * @param taskId 任务ID
     * @return 执行信息
     */
    private static List<BatchOperatorRuntimeEntity> getExecuteInfoFromRedis(String taskId) {
        String taskInfoJsonString = JedisConnection.getField(String.format("moye-batch-engine:task:%s", taskId), "executeInfo");
        return JsonUtils.toList(taskInfoJsonString, BatchOperatorRuntimeEntity.class);
    }

    /**
     * 存储每个算子执行记录到redis
     *
     * @param batchId 执行id
     * @param record 执行记录
     */
    private static void saveOperatorExecutionRecordToRedis(String batchId, BatchOperatorExecutionRecord record) {
        JedisConnection.pushElementAndNotify(String.format("moye-batch-engine:task-execution-record:%s", batchId), JsonUtils.toJsonString(record), "moye-batch-engine:task-execution-record");
    }

    /**
     * 将DataFrame转换为结构化数据
     *
     * @param resultDf DataFrame
     * @return 结构化数据列表
     */
    private static List<Map<String, Object>> convertToStructuredData(Dataset<Row> resultDf) {
        int rowNum = 10;
        StructType schema = resultDf.schema();
        // 数据转型成字符串
        Dataset<Row> convertedDf = resultDf.limit(rowNum).select(Arrays.stream(resultDf.columns())
            .map(colName -> {
                // 二进制类型保持原样，其他类型转为字符串
                if (schema.apply(colName).dataType() instanceof BinaryType) {
                    return new Column(colName);
                } else {
                    return new Column(colName).cast(DataTypes.StringType);
                }
            }).toArray(Column[]::new));

        List<Map<String, Object>> structuredData = new ArrayList<>();
        convertedDf.takeAsList(rowNum).forEach(row -> {
            Map<String, Object> rowData = new HashMap<>();
            for (StructField field : schema.fields()) {
                rowData.put(field.name(), row.getAs(field.name()));
            }
            structuredData.add(rowData);
        });

        return structuredData;
    }

}
