package com.trs.moye.bi.engine.indicator.processor.condition;

import com.trs.moye.base.data.service.entity.Condition;
import com.trs.moye.base.data.service.entity.ValueObject;
import com.trs.moye.base.data.service.enums.DataServiceConditionType;
import com.trs.moye.base.data.service.enums.EvaluateOperator;
import com.trs.moye.bi.engine.indicator.util.ExceptionLogOptimizer;
import com.trs.moye.bi.engine.indicator.util.ExceptionType;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Stack;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

/**
 * 内存条件评估器
 * <p>
 * 在内存中对数据记录进行条件评估和过滤，支持：
 * <ul>
 *   <li>基础比较操作：等于、不等于、大于、小于等</li>
 *   <li>集合操作：包含、不包含（精准和模糊）</li>
 *   <li>空值判断：为空、不为空</li>
 *   <li>正则表达式匹配</li>
 *   <li>逻辑操作：AND、OR、NOT、括号</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * @since 2025/08/13
 */
@Slf4j
public class MemoryConditionEvaluator {

    /**
     * 评估条件并过滤数据
     * <p>
     * 对输入的数据列表应用条件过滤，返回满足条件的记录
     * </p>
     *
     * @param data       待过滤的数据列表
     * @param conditions 条件列表
     * @return 过滤后的数据列表
     */
    public static List<Map<String, Object>> evaluateConditions(List<Map<String, Object>> data,
        List<Condition> conditions) {
        if (CollectionUtils.isEmpty(data)) {
            log.debug("输入数据为空，返回空列表");
            return new ArrayList<>();
        }

        if (CollectionUtils.isEmpty(conditions)) {
            log.debug("条件列表为空，返回原始数据");
            return new ArrayList<>(data);
        }

        List<Map<String, Object>> filteredData = new ArrayList<>();
        int totalRecords = data.size();

        for (Map<String, Object> record : data) {
            try {
                if (evaluateRecord(record, conditions)) {
                    filteredData.add(record);
                }
            } catch (Exception e) {
                ExceptionLogOptimizer.logSampledWarning(ExceptionType.COMPARE_ERROR,
                    "评估记录时发生异常，跳过该记录: {}", e.getMessage());
            }
        }

        log.info("内存条件评估完成，原始记录数: {}, 过滤后记录数: {}", totalRecords, filteredData.size());
        return filteredData;
    }

    /**
     * 评估单条记录是否满足条件
     * <p>
     * 使用栈结构处理复杂的逻辑表达式，支持括号、AND、OR、NOT等操作
     * </p>
     *
     * @param record     数据记录
     * @param conditions 条件列表
     * @return 如果记录满足条件返回true，否则返回false
     */
    private static boolean evaluateRecord(Map<String, Object> record, List<Condition> conditions) {
        if (CollectionUtils.isEmpty(conditions)) {
            return true;
        }

        Stack<Boolean> operandStack = new Stack<>();
        Stack<EvaluateOperator> operatorStack = new Stack<>();

        for (Condition condition : conditions) {
            if (DataServiceConditionType.EXPRESSION.equals(condition.getType())) {
                // 表达式条件，计算结果并压入操作数栈
                boolean result = evaluateExpression(record, condition);
                operandStack.push(result);

                // 处理栈顶的操作符
                processOperators(operandStack, operatorStack, false);

            } else if (DataServiceConditionType.LOGIC.equals(condition.getType())) {
                EvaluateOperator operator = condition.getOperator();

                if (EvaluateOperator.LEFT_BRACKET.equals(operator)) {
                    // 左括号，压入操作符栈
                    operatorStack.push(operator);
                } else if (EvaluateOperator.RIGHT_BRACKET.equals(operator)) {
                    // 右括号，处理到左括号为止的所有操作符
                    processOperators(operandStack, operatorStack, true);
                } else {
                    // 其他逻辑操作符
                    operatorStack.push(operator);
                }
            }
        }

        // 处理剩余的操作符
        processOperators(operandStack, operatorStack, false);

        // 返回最终结果
        return operandStack.isEmpty() || operandStack.peek();
    }

    /**
     * 处理操作符栈中的操作符
     *
     * @param operandStack  操作数栈
     * @param operatorStack 操作符栈
     * @param untilBracket  是否处理到左括号为止
     */
    private static void processOperators(Stack<Boolean> operandStack, Stack<EvaluateOperator> operatorStack,
        boolean untilBracket) {
        while (!operatorStack.isEmpty()) {
            EvaluateOperator operator = operatorStack.peek();

            if (untilBracket && EvaluateOperator.LEFT_BRACKET.equals(operator)) {
                operatorStack.pop(); // 移除左括号
                break;
            }

            if (EvaluateOperator.LEFT_BRACKET.equals(operator)) {
                break;
            }

            operatorStack.pop();
            applyOperator(operandStack, operator);
        }
    }

    /**
     * 应用逻辑操作符
     *
     * @param operandStack 操作数栈
     * @param operator     操作符
     */
    private static void applyOperator(Stack<Boolean> operandStack, EvaluateOperator operator) {
        if (EvaluateOperator.NOT.equals(operator)) {
            // NOT操作符，取反栈顶元素
            if (!operandStack.isEmpty()) {
                boolean operand = operandStack.pop();
                operandStack.push(!operand);
            }
        } else if (EvaluateOperator.AND.equals(operator)) {
            // AND操作符，弹出两个操作数进行AND运算
            if (operandStack.size() >= 2) {
                boolean right = operandStack.pop();
                boolean left = operandStack.pop();
                operandStack.push(left && right);
            }
        } else if (EvaluateOperator.OR.equals(operator)) {
            // OR操作符，弹出两个操作数进行OR运算
            if (operandStack.size() >= 2) {
                boolean right = operandStack.pop();
                boolean left = operandStack.pop();
                operandStack.push(left || right);
            }
        }
    }

    /**
     * 评估表达式条件
     *
     * @param record    数据记录
     * @param condition 条件
     * @return 评估结果
     */
    private static boolean evaluateExpression(Map<String, Object> record, Condition condition) {
        String fieldName = condition.getKey().getEnName();
        Object fieldValue = record.get(fieldName);
        EvaluateOperator operator = condition.getOperator();
        List<ValueObject> conditionValues = condition.getValues();

        try {
            return switch (operator) {
                case EQ -> evaluateEquals(fieldValue, conditionValues);
                case NEQ -> !evaluateEquals(fieldValue, conditionValues);
                case GT -> evaluateGreaterThan(fieldValue, conditionValues);
                case GTE -> evaluateGreaterThanOrEqual(fieldValue, conditionValues);
                case LT -> evaluateLessThan(fieldValue, conditionValues);
                case LTE -> evaluateLessThanOrEqual(fieldValue, conditionValues);
                case IN -> evaluateIn(fieldValue, conditionValues);
                case NOT_IN -> !evaluateIn(fieldValue, conditionValues);
                case CONTAIN -> evaluateContain(fieldValue, conditionValues);
                case NOT_CONTAIN -> !evaluateContain(fieldValue, conditionValues);
                case IS_NULL -> fieldValue == null;
                case IS_NOT_NULL -> fieldValue != null;
                case REGEX -> evaluateRegex(fieldValue, conditionValues);
                default -> {
                    ExceptionLogOptimizer.logSampledWarning(ExceptionType.COMPARE_ERROR, "不支持的操作符: {}",
                        operator);
                    yield false;
                }
            };
        } catch (Exception e) {
            ExceptionLogOptimizer.logSampledWarning(ExceptionType.COMPARE_ERROR,
                "评估表达式时发生异常，字段: {}, 操作符: {}, 错误: {}",
                fieldName, operator, e.getMessage());
            return false;
        }
    }

    /**
     * 评估等于条件
     *
     * @param fieldValue      字段值
     * @param conditionValues 条件值列表
     * @return 评估结果
     */
    private static boolean evaluateEquals(Object fieldValue, List<ValueObject> conditionValues) {
        if (CollectionUtils.isEmpty(conditionValues)) {
            return false;
        }

        String conditionValue = conditionValues.get(0).getValue();
        return Objects.equals(convertToString(fieldValue), conditionValue);
    }

    /**
     * 评估大于条件
     *
     * @param fieldValue      字段值
     * @param conditionValues 条件值列表
     * @return 评估结果
     */
    private static boolean evaluateGreaterThan(Object fieldValue, List<ValueObject> conditionValues) {
        if (CollectionUtils.isEmpty(conditionValues)) {
            return false;
        }

        return compareNumbers(fieldValue, conditionValues.get(0).getValue()) > 0;
    }

    /**
     * 评估大于等于条件
     *
     * @param fieldValue      字段值
     * @param conditionValues 条件值列表
     * @return 评估结果
     */
    private static boolean evaluateGreaterThanOrEqual(Object fieldValue, List<ValueObject> conditionValues) {
        if (CollectionUtils.isEmpty(conditionValues)) {
            return false;
        }

        return compareNumbers(fieldValue, conditionValues.get(0).getValue()) >= 0;
    }

    /**
     * 评估小于条件
     *
     * @param fieldValue      字段值
     * @param conditionValues 条件值列表
     * @return 评估结果
     */
    private static boolean evaluateLessThan(Object fieldValue, List<ValueObject> conditionValues) {
        if (CollectionUtils.isEmpty(conditionValues)) {
            return false;
        }

        return compareNumbers(fieldValue, conditionValues.get(0).getValue()) < 0;
    }

    /**
     * 评估小于等于条件
     *
     * @param fieldValue      字段值
     * @param conditionValues 条件值列表
     * @return 评估结果
     */
    private static boolean evaluateLessThanOrEqual(Object fieldValue, List<ValueObject> conditionValues) {
        if (CollectionUtils.isEmpty(conditionValues)) {
            return false;
        }

        return compareNumbers(fieldValue, conditionValues.get(0).getValue()) <= 0;
    }

    /**
     * 评估包含条件（精准匹配）
     *
     * @param fieldValue      字段值
     * @param conditionValues 条件值列表
     * @return 评估结果
     */
    private static boolean evaluateIn(Object fieldValue, List<ValueObject> conditionValues) {
        if (CollectionUtils.isEmpty(conditionValues)) {
            return false;
        }

        String fieldStr = convertToString(fieldValue);
        return conditionValues.stream()
            .anyMatch(valueObj -> Objects.equals(fieldStr, valueObj.getValue()));
    }

    /**
     * 评估包含条件（模糊匹配）
     *
     * @param fieldValue      字段值
     * @param conditionValues 条件值列表
     * @return 评估结果
     */
    private static boolean evaluateContain(Object fieldValue, List<ValueObject> conditionValues) {
        if (CollectionUtils.isEmpty(conditionValues)) {
            return false;
        }

        String fieldStr = convertToString(fieldValue);
        if (fieldStr == null) {
            return false;
        }

        return conditionValues.stream()
            .anyMatch(valueObj -> fieldStr.contains(valueObj.getValue()));
    }

    /**
     * 评估正则表达式条件
     *
     * @param fieldValue      字段值
     * @param conditionValues 条件值列表
     * @return 评估结果
     */
    private static boolean evaluateRegex(Object fieldValue, List<ValueObject> conditionValues) {
        if (CollectionUtils.isEmpty(conditionValues)) {
            return false;
        }

        String fieldStr = convertToString(fieldValue);
        if (fieldStr == null) {
            return false;
        }

        try {
            String regexPattern = conditionValues.get(0).getValue();
            Pattern pattern = Pattern.compile(regexPattern);
            return pattern.matcher(fieldStr).matches();
        } catch (Exception e) {
            ExceptionLogOptimizer.logSampledWarning(ExceptionType.COMPARE_ERROR, "正则表达式匹配失败: {}",
                e.getMessage());
            return false;
        }
    }

    /**
     * 比较数字
     *
     * @param fieldValue     字段值
     * @param conditionValue 条件值
     * @return 比较结果：负数表示小于，0表示等于，正数表示大于
     */
    private static int compareNumbers(Object fieldValue, String conditionValue) {
        try {
            BigDecimal fieldNumber = convertToBigDecimal(fieldValue);
            BigDecimal conditionNumber = new BigDecimal(conditionValue);
            return fieldNumber.compareTo(conditionNumber);
        } catch (Exception e) {
            ExceptionLogOptimizer.logSampledWarning(ExceptionType.COMPARE_ERROR, "数字比较失败，使用字符串比较: {}",
                e.getMessage());
            String fieldStr = convertToString(fieldValue);
            return fieldStr != null ? fieldStr.compareTo(conditionValue) : -1;
        }
    }

    /**
     * 转换为BigDecimal
     *
     * @param value 值
     * @return BigDecimal值
     */
    private static BigDecimal convertToBigDecimal(Object value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }

        if (value instanceof Number) {
            return new BigDecimal(value.toString());
        }

        return new BigDecimal(value.toString());
    }

    /**
     * 转换为字符串
     *
     * @param value 值
     * @return 字符串值
     */
    private static String convertToString(Object value) {
        return value != null ? value.toString() : null;
    }
}
