package com.trs.moye.bi.engine.indicator.processor;

import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.data.indicator.entity.DataModelIndicatorField;
import com.trs.moye.base.data.indicator.entity.IndicatorApplicationFieldConfig;
import com.trs.moye.bi.engine.indicator.CalculationFunction;
import com.trs.moye.bi.engine.indicator.context.IndicatorDataContext;
import com.trs.moye.bi.engine.indicator.context.QueryContext;
import com.trs.moye.bi.engine.indicator.exception.IndicatorQueryException;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 指标计算服务
 * <p>
 * 负责执行指标的计算逻辑，包括：
 * <ul>
 *   <li>应用字段的计算函数执行</li>
 *   <li>计算结果的收集和管理</li>
 *   <li>计算结果与原始数据的合并</li>
 * </ul>
 * 支持多种计算函数，按照字段的执行顺序依次进行计算，确保依赖关系的正确处理
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/28
 */
@Slf4j
@Service
public class IndicatorCalculationService {

    /**
     * 执行指标计算
     * <p>
     * 遍历所有启用的应用字段，按照执行顺序依次进行计算 计算过程包括：
     * <ul>
     *   <li>筛选启用的应用字段</li>
     *   <li>按执行顺序排序</li>
     *   <li>依次执行每个字段的计算函数</li>
     *   <li>收集所有计算结果</li>
     * </ul>
     * </p>
     *
     * @param context 查询上下文，包含字段配置和计算所需的数据
     * @return 所有字段的计算结果映射，键为字段名，值为该字段的所有计算值
     * @throws IndicatorQueryException 当指标计算失败时抛出
     */
    public Map<String, Map<String, Object>> performCalculations(QueryContext context) {
        try {
            log.debug("开始执行指标计算, dataModelId: {}", context.getDataModelId());

            // 获取启用的应用字段并按执行顺序排序
            List<DataModelIndicatorField> applicationFields = context.getFieldGroups()
                .applicationFields().stream()
                .filter(DataModelIndicatorField::isEnable)
                .filter(e -> e.getConfig() instanceof IndicatorApplicationFieldConfig)
                .sorted(Comparator.comparing(DataModelIndicatorField::getExecuteOrder,
                    Comparator.nullsLast(Comparator.naturalOrder())))
                .toList();

            // 依次执行每个字段的计算
            for (DataModelIndicatorField field : applicationFields) {
                executeFieldCalculation(field);
            }

            // 获取所有计算结果
            Map<String, Map<String, Object>> result = IndicatorDataContext.getAllCalculatedValues();
            log.debug("指标计算完成, 计算字段数量: {}", result.size());

            return result;

        } catch (Exception e) {
            log.error("指标计算失败, dataModelId: {}", context.getDataModelId(), e);
            throw IndicatorQueryException.calculationError("指标计算", e);
        }
    }

    /**
     * 执行单个字段的计算
     * <p>
     * 根据字段配置中的计算函数名称，查找对应的计算函数并执行计算 计算完成后将结果存储到数据上下文中，供后续步骤使用
     * </p>
     *
     * @param field 待计算的指标字段，包含计算函数配置
     * @throws IndicatorQueryException 当字段计算失败时抛出
     */
    private void executeFieldCalculation(DataModelIndicatorField field) {
        try {
            IndicatorApplicationFieldConfig config = (IndicatorApplicationFieldConfig) field.getConfig();
            String functionName = config.getFunction();

            if (StringUtils.isNotBlank(functionName)) {
                // 根据函数名获取计算函数
                CalculationFunction calculateFunction = CalculationFunction.fromName(functionName);
                if (calculateFunction != null) {
                    // 执行计算并获取结果
                    Map<String, Object> calculatedValues = calculateFunction.apply(config);
                    if (calculatedValues != null && !calculatedValues.isEmpty()) {
                        // 将计算结果存储到数据上下文
                        IndicatorDataContext.addCalculatedValue(field.getEnName(), calculatedValues);
                    }
                } else {
                    log.warn("没有找到函数 {} 对应的计算函数", functionName);
                }
            }
        } catch (Exception e) {
            log.error("字段计算失败, fieldName: {}", field.getEnName(), e);
            throw IndicatorQueryException.calculationError(field.getEnName(), e);
        }
    }

    /**
     * 合并计算结果到数据中（向后兼容方法）
     * <p>
     * 为了保持向后兼容性，当没有提供字段类型映射时，缺少的字段值将不会被设置
     * </p>
     *
     * @param resultData          原始结果数据，计算结果将合并到这些数据行中
     * @param allCalculatedValues 所有字段的计算结果映射
     * @throws IndicatorQueryException 当合并计算结果失败时抛出
     */
    public void mergeCalculationResults(List<Map<String, Object>> resultData,
        Map<String, Map<String, Object>> allCalculatedValues) {
        mergeCalculationResults(resultData, allCalculatedValues, new HashMap<>());
    }

    /**
     * 合并计算结果到数据中
     * <p>
     * 将所有字段的计算结果合并到原始数据行中合并过程：
     * <ul>
     *   <li>使用键生成器为每行数据生成唯一键</li>
     *   <li>根据键匹配计算结果和数据行</li>
     *   <li>将匹配的计算结果添加到对应的数据行中</li>
     *   <li>当计算结果中缺少某个字段的值时，根据字段类型设置默认值</li>
     * </ul>
     * </p>
     *
     * @param resultData          原始结果数据，计算结果将合并到这些数据行中
     * @param allCalculatedValues 所有字段的计算结果映射
     * @param fieldTypeMap        字段名到字段类型的映射，用于设置默认值
     * @throws IndicatorQueryException 当合并计算结果失败时抛出
     */
    public void mergeCalculationResults(List<Map<String, Object>> resultData,
        Map<String, Map<String, Object>> allCalculatedValues, Map<String, FieldType> fieldTypeMap) {
        if (allCalculatedValues.isEmpty()) {
            return;
        }

        try {
            log.debug("开始合并计算结果, 计算字段数量: {}", allCalculatedValues.size());

            // 获取键生成器
            Function<Map<String, Object>, String> keyGenerator = IndicatorDataContext.getKeyGenerator()
                .orElseThrow(() -> new IllegalStateException("键生成器未初始化"));

            // 遍历每行数据，合并对应的计算结果
            for (Map<String, Object> row : resultData) {
                String key = keyGenerator.apply(row);
                // 遍历所有计算字段，将匹配的结果添加到当前行
                allCalculatedValues.forEach((fieldName, calculatedValues) -> {
                    Object value;
                    if (calculatedValues.containsKey(key)) {
                        value = calculatedValues.get(key);
                    } else {
                        // 当计算结果中缺少该字段的值时，根据字段类型设置默认值
                        FieldType fieldType = fieldTypeMap.get(fieldName);
                        value = getDefaultValueByFieldType(fieldType);
                        log.debug("字段 {} 缺少计算值，使用默认值: {}", fieldName, value);
                    }

                    // 确保类型转换的安全性
                    try {
                        row.put(fieldName, value);
                    } catch (Exception e) {
                        log.warn("字段 {} 值设置失败，使用null值: {}", fieldName, e.getMessage());
                        row.put(fieldName, null);
                    }
                });
            }

            log.debug("计算结果合并完成");

        } catch (Exception e) {
            log.error("合并计算结果失败", e);
            throw IndicatorQueryException.dataProcessingError("合并计算结果", e);
        }
    }

    /**
     * 根据字段类型获取默认值
     * <p>
     * 为不同的数据类型提供合适的默认值：
     * <ul>
     *   <li>数字类型：返回对应类型的0值</li>
     *   <li>字符串类型：返回空字符串</li>
     *   <li>布尔类型：返回false</li>
     *   <li>其他类型：返回null</li>
     * </ul>
     * </p>
     *
     * @param fieldType 字段类型
     * @return 对应类型的默认值
     */
    private Object getDefaultValueByFieldType(FieldType fieldType) {
        if (fieldType == null) {
            return null;
        }

        try {
            return switch (fieldType) {
                case INT, LONG, FLOAT, DOUBLE, SHORT, DECIMAL -> 0;
                case STRING, CHAR, TEXT -> "";
                case BOOLEAN -> false;
                default -> null;
            };
        } catch (Exception e) {
            log.warn("获取字段类型 {} 的默认值失败: {}", fieldType, e.getMessage());
            return null;
        }
    }
}
