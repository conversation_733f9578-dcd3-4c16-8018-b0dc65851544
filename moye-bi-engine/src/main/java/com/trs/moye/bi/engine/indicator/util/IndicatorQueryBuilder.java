package com.trs.moye.bi.engine.indicator.util;

import com.trs.moye.base.common.request.SortField;
import com.trs.moye.base.common.utils.DateTimeUtils;
import com.trs.moye.base.common.utils.conditions.SqlUtils;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.indicator.entity.IndicatorDataSearchParams;
import com.trs.moye.base.data.indicator.entity.IndicatorSortField;
import com.trs.moye.base.data.indicator.entity.StatisticPeriod;
import com.trs.moye.base.data.indicator.enums.IndicatorSortOrder;
import com.trs.moye.base.data.service.entity.Condition;
import com.trs.moye.bi.engine.indicator.IndicatorServiceNew.FieldGroups;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 指标查询生成器
 *
 * <AUTHOR>
 * @since 2025/07/13 14:30:56
 */
public class IndicatorQueryBuilder {

    private final IndicatorDataSearchParams params;
    private final FieldGroups fieldGroups;
    private final DataConnection dataConnection;

    public IndicatorQueryBuilder(IndicatorDataSearchParams params, FieldGroups fieldGroups,
        DataConnection dataConnection) {
        this.params = params;
        this.fieldGroups = fieldGroups;
        this.dataConnection = dataConnection;
    }

    /**
     * 构建条件查询 SQL
     * <p>
     * 构建包含条件过滤的查询SQL语句，会自动跳过应用指标字段的条件处理 应用指标字段在后续的计算阶段处理，不应在基础数据查询阶段进行过滤
     * </p>
     *
     * @return 构建完成的SQL查询语句
     * <AUTHOR>
     * @since 2025/07/13 14:31:06
     */
    public String buildConditionalQuerySql() {
        // 获取应用指标字段名称列表，用于过滤条件
        List<String> applicationFieldNames = fieldGroups.getApplicationFieldNames();

        // 过滤掉应用指标字段的条件，只保留元字段的条件用于基础数据查询
        List<Condition> filteredConditions = filterNonApplicationFieldConditions(
            params.getConditions(), applicationFieldNames);

        // 使用过滤后的条件构建查询语句
        String selectStatement = SqlUtils.getQuerySql(filteredConditions, dataConnection.getConnectionType(),
            params.getTableName());
        StringBuilder sqlBuilder = new StringBuilder(selectStatement);

        // 添加时间条件
        String timeCondition = buildTimeCondition(fieldGroups.startTimeField(), fieldGroups.endTimeField(),
            params.getStatisticPeriod());

        if (selectStatement.toLowerCase().contains("where")) {
            sqlBuilder.append(" AND ").append(timeCondition);
        } else {
            sqlBuilder.append(" WHERE ").append(timeCondition);
        }

        // 处理排序字段
        IndicatorSortField sortField = params.getSortField();
        if (shouldApplySqlSort(sortField, applicationFieldNames)) {
            List<SortField> sortFields = Collections.singletonList(
                new SortField(sortField.getField(), SortField.Order.valueOf(sortField.getOrder().name()))
            );
            String orderBy = SqlUtils.buildSortStatement(sortFields, dataConnection.getConnectionType());
            sqlBuilder.append(" ").append(orderBy);
        }

        return sqlBuilder.toString();
    }

    /**
     * 过滤掉应用指标字段的条件
     * <p>
     * 遍历所有条件，移除那些字段名属于应用指标字段的条件 应用指标字段的条件将在后续的内存过滤阶段处理，不应在基础查询中使用
     * </p>
     *
     * @param conditions            原始条件列表
     * @param applicationFieldNames 应用指标字段名称列表
     * @return 过滤后的条件列表，不包含应用指标字段的条件
     */
    private List<Condition> filterNonApplicationFieldConditions(List<Condition> conditions,
        List<String> applicationFieldNames) {
        if (conditions == null || conditions.isEmpty()) {
            return conditions;
        }

        return conditions.stream()
            .filter(condition -> !isApplicationFieldCondition(condition, applicationFieldNames))
            .collect(Collectors.toList());
    }

    /**
     * 判断条件是否为应用指标字段条件
     * <p>
     * 检查条件中的字段是否属于应用指标字段 只有表达式类型的条件才需要检查字段名，逻辑连接符条件直接保留
     * </p>
     *
     * @param condition             待检查的条件
     * @param applicationFieldNames 应用指标字段名称列表
     * @return 如果是应用指标字段条件返回true，否则返回false
     */
    private boolean isApplicationFieldCondition(Condition condition, List<String> applicationFieldNames) {
        // 如果不是表达式类型的条件（如逻辑连接符），则保留
        if (condition.getType() == null || condition.getKey() == null) {
            return false;
        }

        // 检查字段名是否在应用指标字段列表中
        String fieldName = condition.getKey().getEnName();
        return applicationFieldNames.contains(fieldName);
    }

    private boolean shouldApplySqlSort(IndicatorSortField sortField, List<String> applicationFieldNames) {
        return sortField != null
            && !applicationFieldNames.contains(sortField.getField())
            && (sortField.getOrder() == IndicatorSortOrder.ASC || sortField.getOrder() == IndicatorSortOrder.DESC);
    }

    /**
     * 构建历史查询 SQL
     *
     * @param tableName 表名称
     * @param period    时期
     * @return {@link String }
     * <AUTHOR>
     * @since 2025/07/13 14:31:39
     */
    public String buildHistoricalQuerySql(String tableName, StatisticPeriod period) {
        String timeCondition = buildTimeCondition(fieldGroups.startTimeField(), fieldGroups.endTimeField(), period);
        return String.format("SELECT * FROM %s WHERE %s", tableName, timeCondition);
    }

    private String buildTimeCondition(String startTimeField, String endTimeField, StatisticPeriod period) {
        return String.format("%s >= '%s' AND %s <= '%s'", startTimeField,
            DateTimeUtils.formatStr(period.getStartTime()), endTimeField,
            DateTimeUtils.formatStr(period.getEndTime()));
    }
}
