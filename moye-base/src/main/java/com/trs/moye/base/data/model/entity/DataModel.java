package com.trs.moye.base.data.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.execute.ExecuteModeEnum;
import com.trs.moye.base.data.model.enums.AuditFieldEnum;
import com.trs.moye.base.data.model.enums.CreateModeEnum;
import com.trs.moye.base.data.model.enums.ModelExecuteStatus;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.xxljob.XxlTaskParam;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据建模
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "data_model", autoResultMap = true)
public class DataModel extends AuditBaseEntity {

    /**
     * 中文名
     */
    private String zhName;

    /**
     * 英文名
     */
    private String enName;

    /**
     * 创建模式： 数据接入、逆向建模、视图
     */
    private CreateModeEnum createMode;

    /**
     * 任务执行状态：1开启，2关闭
     */
    private ModelExecuteStatus executeStatus;

    /**
     * 业务分类id
     */
    private Integer businessCategoryId;

    /**
     * 分层
     */
    private ModelLayer layer;

    /**
     * 说明
     */
    private String description;

    /**
     * 数据源
     */
    @TableField(exist = false)
    private List<DataSourceConfig> dataSource;
    /**
     * 数据存储
     */
    @TableField(exist = false)
    private List<DataStorage> dataStorages;

    /**
     * 字段
     */
    @TableField(exist = false)
    private List<DataModelField> fields;

    /**
     * 执行配置
     */
    @TableField(exist = false)
    private DataModelExecuteConfig executeConfig;

    /**
     * 调度配置
     */
    @TableField(exist = false)
    private DataModelScheduleConfig scheduleConfig;


    /**
     * 是否治理
     */
    private Boolean isArranged;


    /**
     * 元数据标准 编号, 根据 元数据标准 创建的 数据建模 这个字段不为空
     */
    private Integer metaDataStandardId;

    /**
     * 是否同步字段
     */
    private boolean isSyncField;
    /**
     * 是否发布到数据智能体
     */
    private Boolean isMcpPublished;



    /**
     * 获取字段id列表 用于更新存储
     *
     * @return 字段id列表
     */
    @JsonIgnore
    public List<Integer> getFieldIds() {
        return fields.stream().mapToInt(DataModelField::getId).boxed().collect(Collectors.toList());
    }

    /**
     * 构建建模任务名称
     *
     * @return 建模任务名称
     */
    public String buildModelTaskName() {
        return layer.getLabel() + "-" + zhName + "-" + id;
    }

    /**
     * 返回指定数存储
     *
     * @param storageId 存储id
     * @return 存储
     */
    public DataStorage getDataStorage(Integer storageId) {
        return dataStorages.stream().filter(dataStorage -> dataStorage.getId().equals(storageId)).findFirst()
            .orElseThrow(() -> new IllegalArgumentException(String.format("未找到id为%s存储点", storageId)));
    }

    /**
     * 第一个数据源是否是http类型
     *
     * @return 是否是http类型
     */
    public boolean isApiType() {
        return ConnectionType.HTTP.equals(this.dataSource.get(0).getConnection().getConnectionType());
    }

    /**
     * 获取第一数据源连接id
     *
     * @return {@link Integer }
     * <AUTHOR>
     * @since 2024/12/27 19:24:56
     */
    public Integer getFirstSourceConnectionId() {
        return this.dataSource.stream()
            .findFirst()
            .map(DataSourceConfig::getConnectionId)
            .orElseThrow(() -> new IllegalArgumentException(
                String.format("未找到数据建模【%d】对应的数据源", id)));
    }

    /**
     * 构建xxl任务参数
     *
     * @return {@link XxlTaskParam}
     */
    public String buildXxlTaskParam() {
        return JsonUtils.toJsonString(new XxlTaskParam(id, zhName));
    }

    /**
     * 是否包含分区字段
     *
     * @return 是否包含分区字段
     */
    @JsonIgnore
    public boolean hasPartitionColumn() {
        return dataStorages.stream()
            .anyMatch(dataStorage -> dataStorage.getConnection().getConnectionType().equals(ConnectionType.HIVE)
                && dataStorage.isPartitioned());
    }

    /**
     * 是否为增量任务
     *
     * @return 是否为增量任务
     */
    @JsonIgnore
    public boolean isIncreaseJob() {
        //增量信息非空 或者 数据源非空且数据源第一个数据源的连接类型为SFTP/FTP/HTTP类型(默认使用增量)
        boolean incrementInfoNonNullFlag = Objects.nonNull(executeConfig.getIncrementInfo());
        boolean dataSourceNonNullFlag =
            Objects.nonNull(dataSource) && !dataSource.isEmpty() && Objects.nonNull(dataSource.get(0).getSettings());
        boolean fileType = dataSource.get(0).getSettings().getConnectionType().isFileType();
        boolean httpType = dataSource.get(0).getSettings().getConnectionType().isHttpType();

        return incrementInfoNonNullFlag || (dataSourceNonNullFlag && (fileType || httpType));
    }

    private static final Set<String> REQUIRED_AUDIT_FIELDS = new HashSet<>(Arrays.asList(
        AuditFieldEnum.TRS_MOYE_INPUT_TIME.getName(),
        AuditFieldEnum.TRS_MOYE_BATCH_NO.getName(),
        AuditFieldEnum.TRS_MOYE_TASK_ID.getName()
    ));

    private static final String TRS_MOYE_RECORD_ID = "trs_moye_record_id";

    /**
     * 是否有审计字段
     *
     * @return 是否有审计字段
     */
    @JsonIgnore
    public boolean haveAuditField() {
        Set<String> fieldNames = fields.stream().map(DataModelField::getEnName).collect(Collectors.toSet());
        return fieldNames.containsAll(REQUIRED_AUDIT_FIELDS);
    }

    /**
     * 是否有记录ID
     *
     * @return 是否有记录ID
     */
    @JsonIgnore
    public boolean haveMoyeRecordId() {
        Set<String> fieldNames = fields.stream().map(DataModelField::getEnName).collect(Collectors.toSet());
        return fieldNames.contains(TRS_MOYE_RECORD_ID);
    }

    /**
     * 判断是否包含字段
     *
     * @param field 字段
     * @return 是否包含
     */
    @JsonIgnore
    public boolean contains(DataModelField field) {
        return fields.stream().anyMatch(f -> f.getEnName().equals(field.getEnName()));
    }

    /**
     * 移除审计字段
     *
     * @return 移除审计字段后的字段
     */
    @JsonIgnore
    public List<DataModelField> getFieldsWithOutAudit() {
        // 移除指定的字段
        Set<Integer> createTableFieldIds = dataStorages.stream()
            .flatMap(dataStorage -> dataStorage.getFieldIds().stream())
            .collect(Collectors.toSet());
        // 移除未建表的字段
        fields.removeIf(field -> !createTableFieldIds.contains(field.getId()));
        return fields.stream()
            .filter(field -> !field.shouldIgnore() && !field.isPartition())
            .collect(Collectors.toList());
    }

    /**
     * 获取第一个数据源
     *
     * @return 第一个数据源
     */
    @JsonIgnore
    public DataSourceConfig getFirstDataSource() {
        return dataSource.get(0);
    }

    /**
     * 判断是否为实时任务
     *
     * @return 是否为实时任务
     */
    @JsonIgnore
    public boolean isRealTimeTask() {
        if (Objects.isNull(scheduleConfig) || Objects.isNull(scheduleConfig.getExecuteMode())) {
            throw new IllegalArgumentException("调度配置或执行模式不能为空");
        }
        return scheduleConfig.getExecuteMode().equals(ExecuteModeEnum.REALTIME);
    }

    /**
     * 第一个数据源是否是http类型
     *
     * @return 是否是http类型
     */
    public boolean isFileType() {
        return dataSource.get(0).getConnection().getConnectionType().isFileType();
    }
}