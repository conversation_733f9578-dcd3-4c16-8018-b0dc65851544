package com.trs.moye.base.data.service.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.trs.moye.base.common.utils.JsonUtils;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>数据服务condition中的运算符>,<,=,>=,<=,!=</p>
 *
 * <AUTHOR>
 */
@Getter
@Slf4j
public enum EvaluateOperator {

    /**
     * 操作符
     */
    IN("in", "包含（精准）", "$in$") {
        @Override
        public String createAviatorScript(String fieldName, List<String> values) {
            return createListParamScript(fieldName, values);
        }
    },

    CONTAIN("contain", "包含（模糊）", "$fuzzyIn$") {
        @Override
        public String createAviatorScript(String fieldName, List<String> values) {
            return createListParamScript(fieldName, values);
        }
    },

    NOT_IN("notIn", "不包含（精准）", "!$in$") {
        @Override
        public String createAviatorScript(String fieldName, List<String> values) {
            return createListParamScript(fieldName, values);
        }
    },


    NOT_CONTAIN("notContain", "不包含（模糊）", "!$fuzzyIn$") {
        @Override
        public String createAviatorScript(String fieldName, List<String> values) {
            return createListParamScript(fieldName, values);
        }
    },

    IS_NOT_NULL("isNotNull", "不为空", "!$empty$") {
        @Override
        public String createAviatorScript(String fieldName, List<String> values) {
            return createSingleParamScript(fieldName);
        }
    },

    IS_NULL("isNull", "为空", "$empty$") {
        @Override
        public String createAviatorScript(String fieldName, List<String> values) {
            return createSingleParamScript(fieldName);
        }
    },

    GT(">", "大于", "$greaterThan$") {
        @Override
        public String createAviatorScript(String fieldName, List<String> values) {
            return createDoubleParamScript(fieldName, values);
        }
    },

    GTE(">=", "大于等于", "$greaterThanOrEqual$") {
        @Override
        public String createAviatorScript(String fieldName, List<String> values) {
            return createDoubleParamScript(fieldName, values);
        }
    },

    LT("<", "小于", "$lessThan$") {
        @Override
        public String createAviatorScript(String fieldName, List<String> values) {
            return createDoubleParamScript(fieldName, values);
        }
    },

    LTE("<=", "小于等于", "$lessThanOrEqual$") {
        @Override
        public String createAviatorScript(String fieldName, List<String> values) {
            return createDoubleParamScript(fieldName, values);
        }
    },

    EQ("=", "等于", "$equal$") {
        @Override
        public String createAviatorScript(String fieldName, List<String> values) {
            return createDoubleParamScript(fieldName, values);
        }
    },

    NEQ("!=", "不等于", "!$equal$") {
        @Override
        public String createAviatorScript(String fieldName, List<String> values) {
            return createDoubleParamScript(fieldName, values);
        }
    },

    REGEX("regex", "正则表达式", "$regex$") {
        @Override
        public String createAviatorScript(String fieldName, List<String> values) {
            return createRegexParamScript(fieldName, values);
        }
    },

    /**
     * 逻辑判断符
     */
    AND("and", "且", "&&"),

    OR("or", "或", "||"),

    NOT("not", "非", "!"),

    LEFT_BRACKET("(", "(", "("),

    RIGHT_BRACKET(")", ")", ")");

    EvaluateOperator(String value, String name, String engineOperator) {
        this.value = value;
        this.name = name;
        this.engineOperator = engineOperator;
    }

    @JsonValue
    private final String value;
    private final String name;
    private final String engineOperator;

    /**
     * 指定的value是否存在对应的枚举类型
     *
     * @param value value
     * @return 是否存在
     */
    public static boolean isExist(String value) {
        for (EvaluateOperator operator : EvaluateOperator.values()) {
            if (operator.getValue().equals(value)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据value或枚举名称获取枚举
     *
     * @param value value或枚举名称
     * @return {@link EvaluateOperator}
     */
    @JsonCreator
    public static EvaluateOperator fromValue(String value) {
        // 首先尝试通过value字段匹配
        for (EvaluateOperator operator : EvaluateOperator.values()) {
            if (operator.getValue().equals(value)) {
                return operator;
            }
        }

        // 如果通过value字段没有匹配成功，尝试通过枚举名称匹配
        try {
            return EvaluateOperator.valueOf(value);
        } catch (IllegalArgumentException e) {
            // 忽略异常，返回null
        }
        return null;
    }

    /**
     * 生成aviator脚本
     *
     * @param fieldName 字段名
     * @param values    值
     * @return 脚本
     */
    public String createAviatorScript(String fieldName, List<String> values) {
        throw new UnsupportedOperationException("不支持生成aviator脚本: " + this.name);
    }

    /**
     * 生成1个参数的脚本
     *
     * @param fieldName 字段名
     * @return 脚本
     */
    protected String createSingleParamScript(String fieldName) {
        return String.format("(%s($get$('%s')))", engineOperator, fieldName);
    }

    /**
     * 生成2个参数的脚本
     *
     * @param fieldName 字段名
     * @param values    值
     * @return 结果脚本
     */
    protected String createDoubleParamScript(String fieldName, String values) {
        return String.format("(%s($get$('%s'), '%s'))", engineOperator, fieldName, values);
    }

    /**
     * 生成2个参数的脚本；过滤参数只支持单值，多值只取第一个
     *
     * @param fieldName 字段名
     * @param values    值
     * @return 结果脚本
     */
    protected String createDoubleParamScript(String fieldName, List<String> values) {
        if (values == null || values.isEmpty()) {
            log.error("过滤条件参数为空");
            return "1==1";
        }
        if (values.size() > 1) {
            log.warn("过滤条件参数个数大于1，只取第一个参数");
        }
        return createDoubleParamScript(fieldName, values.get(0));
    }

    /**
     * 生成2个参数的脚本；其中第二个参数是多值，以json数组的形式传递
     *
     * @param fieldName 字段名
     * @param values    值
     * @return 结果脚本
     */
    protected String createListParamScript(String fieldName, List<String> values) {
        String valueStr = JsonUtils.toJsonString(values);
        return createDoubleParamScript(fieldName, valueStr);
    }

    /**
     * 生成2个参数的脚本；其中第二个参数是正则表达式，为了防止正则表达式中的特殊字符被转义，所以以base64字符串的形式传递
     *
     * @param fieldName 字段名
     * @param values    值
     * @return 结果脚本
     */
    protected String createRegexParamScript(String fieldName, List<String> values) {
        if (values == null || values.isEmpty()) {
            log.error("正则表达式为空");
            return "1==1";
        }
        if (values.size() > 1) {
            log.warn("过滤条件参数个数大于1，只取第一个参数");
        }
        String valueStr = Base64.getEncoder().encodeToString(values.get(0).getBytes(StandardCharsets.UTF_8));
        return createDoubleParamScript(fieldName, valueStr);
    }

    /**
     * 判断是否是只接收单个参数（而非数组）的运算符
     *
     * @return boolean
     * <AUTHOR>
     * @since 2021/6/28 9:40
     */
    public boolean isSingleParam() {
        List<EvaluateOperator> commonOperators = Arrays.asList(GT, GTE, LT, LTE, EQ, NEQ);
        return commonOperators.contains(this);
    }
}
