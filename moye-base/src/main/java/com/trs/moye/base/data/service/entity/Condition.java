package com.trs.moye.base.data.service.entity;

import com.trs.moye.base.data.service.enums.DataServiceConditionType;
import com.trs.moye.base.data.service.enums.EvaluateOperator;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * condition实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Condition implements Serializable {

    /**
     * 条件类型
     */
    private DataServiceConditionType type;
    /**
     * 字段
     */
    private DataServiceField key;
    /**
     * 字段的值
     */
    private List<ValueObject> values;
    /**
     * 操作符
     */
    private EvaluateOperator operator;

    public static final Condition LEFT_BRACKET = new Condition(DataServiceConditionType.LOGIC, null, Collections.emptyList(), EvaluateOperator.LEFT_BRACKET);

    public static final Condition RIGHT_BRACKET = new Condition(DataServiceConditionType.LOGIC, null, Collections.emptyList(), EvaluateOperator.RIGHT_BRACKET);

    public static final Condition AND = new Condition(DataServiceConditionType.LOGIC, null, Collections.emptyList(), EvaluateOperator.AND);


    /**
     * 条件生成字符串
     *
     * @return 字符串
     */
    public String createString() {
        if (type == DataServiceConditionType.LOGIC) {
            return operator.getName();
        } else {
            return key.getZhName() + "(" + key.getEnName() + ") " + operator.getName() + valueToStr();
        }
    }

    private String valueToStr() {
        if (values.isEmpty()) {
            return "";
        } else if (values.size() == 1) {
            return " " + values.get(0).toString();
        } else {
            return " [" + values.stream()
                         .map(ValueObject::getValue)
                         .collect(Collectors.joining(",")) + "]";
        }
    }

}
