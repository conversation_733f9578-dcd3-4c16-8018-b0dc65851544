package com.trs.moye.stream.engine.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.trs.ai.ty.base.entity.po.TracerData;
import com.trs.ai.ty.base.service.MonitorBufferService;
import com.trs.ai.ty.base.utils.SnowflakeIdUtil;
import com.trs.ai.ty.base.utils.StringUtil;
import com.trs.moye.ability.aviator.AviatorService;
import com.trs.moye.ability.aviator.ExecuteOperator;
import com.trs.moye.ability.constants.Constants;
import com.trs.moye.ability.domain.DataProcessRecord;
import com.trs.moye.ability.domain.DataProcessResult;
import com.trs.moye.ability.domain.DataProcessTrace;
import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.entity.operator.Operator;
import com.trs.moye.ability.entity.operator.OperatorRowType;
import com.trs.moye.ability.exception.PipelineInterruptedException;
import com.trs.moye.ability.response.RetryOperatorResponse;
import com.trs.moye.ability.utils.ThreadLocalUtil;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.stream.engine.cache.CacheService;
import com.trs.moye.stream.engine.domain.entity.ExecuteOperatorBaseInfo;
import com.trs.moye.stream.engine.domain.entity.MessageContext;
import com.trs.moye.stream.engine.domain.response.AbilityCheckResponse;
import com.trs.moye.stream.engine.monitor.LinkMonitorConfigMap;
import com.trs.moye.stream.engine.properties.AbilityCenterGatewayProperties;
import com.trs.moye.stream.engine.properties.LogProperties;
import com.trs.moye.stream.engine.utils.StreamEngineStopWatch;
import com.trs.moye.stream.engine.utils.StreamEngineStopWatch.TaskInfo;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * MsgProcessService接口的实现类。 该服务使用操作管道处理消息。
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MsgProcessServiceImpl implements MsgProcessService {

    private final CacheService cacheService;

    private final MonitorBufferService monitorBufferService;

    private final AbilityCenterGatewayProperties abilityCenterGatewayProperties;

    private final LogProperties logProperties;

    @Value("${timeout.minutes:60}")
    private Integer timeoutMinutes;


    /**
     * 使用与指定数据模型ID关联的操作管道处理消息。
     *
     * @param dataModelId 数据模型的ID
     * @param message     要处理的消息
     * @param isRerun     是否为重跑
     * @return 处理后的消息
     * @throws IllegalArgumentException 如果找不到操作管道
     */
    @Override
    public JsonNode processMessage(Integer dataModelId, boolean isRerun, JsonNode message) {
        dataModelId = Math.abs(dataModelId);
        StreamEngineStopWatch stopWatch = new StreamEngineStopWatch(dataModelId.toString());

        stopWatch.start("查询编排");

        // 获取基础信息
        ExecuteOperatorBaseInfo baseInfo = getBaseInfo(dataModelId);
        stopWatch.stop();

        boolean fullTraceStatus = LinkMonitorConfigMap.getInstance().getFullTraceStatus(baseInfo.getDataModelId());

        // 处理数据
        DataProcessResult dataProcessResult = processMessage(baseInfo, message, stopWatch, fullTraceStatus, isRerun);

        stopWatch.start("发送全链路日志");
        //发送处理记录和链路追踪信息
        send(dataProcessResult.getRecord());
        send(dataProcessResult.getTraces());
        stopWatch.stop();

        //执行时长超过阈值时打印详细日志
        logExceedingTimeLimits(stopWatch);
        return dataProcessResult.getResult();
    }

    @Override
    public DataProcessResult processMessage(ExecuteOperatorBaseInfo baseInfo, JsonNode message,
        StreamEngineStopWatch stopWatch, boolean fullTraceEnable, boolean isRerun) {
        // 初始化线程上下文，确保整条消息处理流程使用同一ThreadLocalUtil
        ThreadLocalUtil.clear();
        ThreadLocalUtil.init(baseInfo.getDataModelId());

        stopWatch.start("字段映射");
        TaskInfo taskInfo = new TaskInfo(null, stopWatch.currentTaskName(), null, null);
        taskInfo.setOperatorTaskInfo(false);
        taskInfo.setInput(JsonUtils.toJsonString(message));

        List<Operator> operators = baseInfo.getOperators();

        // 进行字段映射转换，生成新的消息对象
        JsonNode mappedMessage = mapFields(message, baseInfo);
        // 构建 recordId
        long uuid;
        JsonNode idNode = mappedMessage.get(Constants.RECORD_ID);
        if (idNode == null || idNode.longValue() == 0L) {
            // 如果消息中没有记录ID，则生成一个新的
            uuid = SnowflakeIdUtil.newId();
            ObjectNode msgWithRecordId = mappedMessage.deepCopy();
            msgWithRecordId.put(Constants.RECORD_ID, uuid);
            mappedMessage = msgWithRecordId;
        } else {
            uuid = idNode.longValue();
        }
        ThreadLocalUtil.setRecordIdValue(uuid);
        // 构建标题
        String title = mappedMessage.get(baseInfo.getTitleEnName()) != null
            ? mappedMessage.get(baseInfo.getTitleEnName()).asText() : "";
        final MessageContext messageContext = MessageContext.builder()
            .dataModelId(baseInfo.getDataModelId())
            .dataModelName(baseInfo.getDataModelName())
            .recordId(uuid)
            .title(title)
            .executeId(SnowflakeIdUtil.newId())
            .isRerun(isRerun)
            .build();

        // 应用规则引擎来处理算子执行条件

        List<ExecuteOperator> executeOperators = AviatorService.filterProcessingOperator(operators);

        taskInfo.setOutput(JsonUtils.toJsonString(mappedMessage));
        stopWatch.stop(taskInfo);

        // 执行算子编排
        final JsonNode result = execute(messageContext, executeOperators, mappedMessage, stopWatch);

        // 记录处理结果
        stopWatch.start("处理结果");
        TaskInfo endTask = new TaskInfo(null, stopWatch.currentTaskName(), null, null);
        endTask.setOperatorTaskInfo(false);
        endTask.setInput(JsonUtils.toJsonString(result));
        stopWatch.stop(endTask);

        // 清除上下文
        ThreadLocalUtil.clear();

        // 记录日志
        Integer allOperatorCount = operators.size();
        return createTracerLog(messageContext, stopWatch, fullTraceEnable, allOperatorCount, message, result);
    }

    @Override
    public RetryOperatorResponse retryOperator(Integer dataModelId, Long recordId, Integer operatorId,
        JsonNode message) {
        RetryOperatorResponse response = new RetryOperatorResponse();
        Long startTime = System.currentTimeMillis();
        response.setStartTime(startTime);
        try {
            AbilityCheckResponse checkResponse = checkOperator(dataModelId, recordId, operatorId, message);
            String resultString = JsonUtils.toJsonString(checkResponse.getResult());
            response.setResult(StringUtils.isBlank(resultString) ? "" : resultString);
            if (StringUtil.isNotEmpty(checkResponse.getErrorMessage())) {
                response.setException(checkResponse.getErrorMessage());
                response.setIsException(true);
            } else {
                response.setIsException(false);
            }
        } catch (Exception e) {
            log.error("重试算子错误！", e);
            response.setIsException(true);
            response.setException(e.getMessage());
        }
        Long endTime = System.currentTimeMillis();
        response.setDuration(endTime - startTime);
        return response;
    }

    /**
     * 检查并执行单个算子
     *
     * @param dataModelId 数据模型ID
     * @param recordId    记录ID
     * @param operatorId  要执行的算子ID
     * @param message     输入消息
     * @return 执行结果响应
     */
    private AbilityCheckResponse checkOperator(Integer dataModelId, Long recordId, Integer operatorId,
        JsonNode message) {
        AbilityCheckResponse response = new AbilityCheckResponse();
        try {
            // 初始化线程上下文
            ThreadLocalUtil.clear();
            ThreadLocalUtil.init(dataModelId);
            ThreadLocalUtil.setRecordIdValue(recordId);

            // 获取基础信息和算子
            ExecuteOperatorBaseInfo baseInfo = getBaseInfo(dataModelId);
            Operator operator = findOperator(baseInfo.getOperators(), operatorId);
            List<ExecuteOperator> executeOperators = AviatorService.filterProcessingOperator(
                Collections.singletonList(operator));

            // 创建消息上下文和计时器
            final MessageContext messageContext = MessageContext.builder().build();
            StreamEngineStopWatch stopWatch = new StreamEngineStopWatch(dataModelId.toString());

            // 执行算子
            final JsonNode result = execute(messageContext, executeOperators, message, stopWatch);

            TaskInfo lastTaskInfo = stopWatch.getLastTaskInfo();
            if (Objects.nonNull(lastTaskInfo) && lastTaskInfo.getIsError() == 1) {
                response.setErrorMessage(lastTaskInfo.getErrorMessage());
            }
            // 记录执行时间
            logExceedingTimeLimits(stopWatch);

            // 设置结果
            response.setResult(operator.getOutputBind().getResultMap(result, operator.getOutputFields()));
        } catch (Exception e) {
            log.error("test error: ", e);
            response.setErrorMessage(e.getMessage());
        } finally {
            ThreadLocalUtil.clear();
        }
        return response;
    }

    /**
     * 获取执行基础信息
     *
     * @param dataModelId 数据模型ID
     * @return 执行基础信息
     */
    @Override
    public ExecuteOperatorBaseInfo getBaseInfo(Integer dataModelId) {
        Object cachedBaseInfo = cacheService.getBaseInfo(dataModelId);
        ExecuteOperatorBaseInfo baseInfo = JsonUtils.parseObject(JsonUtils.toJsonString(cachedBaseInfo),
            ExecuteOperatorBaseInfo.class);
        if (baseInfo == null) {
            throw new IllegalArgumentException("OperatorPipeline not found! dataModelId=" + dataModelId);
        }
        if (baseInfo.getOperators() == null) {
            throw new IllegalArgumentException("OperatorPipeline has no operators! dataModelId=" + dataModelId);
        }
        return baseInfo;
    }

    /**
     * 查找指定ID的算子
     *
     * @param operators  算子列表
     * @param operatorId 算子ID
     * @return 找到的算子
     */
    private Operator findOperator(List<Operator> operators, Integer operatorId) {
        return operators.stream()
            .filter(o -> operatorId.equals(o.getId()))
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("Operator not found! operatorId=" + operatorId));
    }

    /**
     * 记录超过时间限制的执行日志
     *
     * @param stopWatch 计时器
     */
    private void logExceedingTimeLimits(StreamEngineStopWatch stopWatch) {
        if (stopWatch.getTotalTimeMillis() > logProperties.getTracePrintLimitMillis()) {
            log.warn(stopWatch.prettyPrint(logProperties.getAbilityPrintLimitMillis()));
        }
    }

    /**
     * 按照字段映射规则转换消息
     *
     * @param message  原始消息
     * @param baseInfo 执行基础信息(包含字段映射和数据模型字段)
     * @return 转换后的消息
     */
    private JsonNode mapFields(JsonNode message, ExecuteOperatorBaseInfo baseInfo) {
        // 如果没有消息或基础信息，则不做转换
        if (message == null || baseInfo == null) {
            return message;
        }
        // TODO: 兼容旧配置：如果fieldMapping和inputFields都为空，则直接返回原消息,后续有需要可删除
        if (ObjectUtils.isEmpty(baseInfo.getFieldMapping()) && ObjectUtils.isEmpty(baseInfo.getInputFields())) {
            return message;
        }
        try {
            // 深拷贝整个消息，保留未映射字段
            ObjectNode result = message.deepCopy();
            // 1. 处理字段映射关系
            applyFieldMapping(message, baseInfo.getFieldMapping(), result);
            // 2. 应用输入字段，以配置勾选的输入字段为准
            applyInputFields(baseInfo.getInputFields(), result);
            // 3. 确保保留记录ID
            if (message.has(Constants.RECORD_ID) && !result.has(Constants.RECORD_ID)) {
                result.set(Constants.RECORD_ID, message.get(Constants.RECORD_ID));
            }
            return result;
        } catch (Exception e) {
            log.error("字段映射转换失败: {}", e.getMessage(), e);
            // 转换失败时返回原始消息
            return message;
        }
    }

    /**
     * 应用字段映射，将源字段映射到目标字段
     *
     * @param message      消息
     * @param fieldMapping 字段映射
     * @param result       结果
     */
    private void applyFieldMapping(JsonNode message, Map<String, List<String>> fieldMapping, ObjectNode result) {
        if (Objects.isNull(fieldMapping)) {
            return;
        }
        fieldMapping.forEach((sourceField, targetFields) -> {
            if (!message.has(sourceField) || ObjectUtils.isEmpty(targetFields)) {
                return;
            }
            JsonNode sourceValue = message.get(sourceField);
            // 移除原字段，防止重复存在
            result.remove(sourceField);
            for (String targetField : targetFields) {
                if (StringUtils.isNotEmpty(targetField)) {
                    result.set(targetField, sourceValue);
                }
            }
        });
    }

    /**
     * 应用输入字段定义
     *
     * @param inputFields 输入字段
     * @param result      结果
     */
    private void applyInputFields(OperatorRowType inputFields, ObjectNode result) {
        if (Objects.isNull(inputFields)) {
            return;
        }
        inputFields.forEach((fieldName, schema) -> {
            // 如果结果中尚未包含此字段
            if (!result.has(fieldName)) {
                // 根据schema类型创建对应的空值
                switch (schema.getType()) {
                    case OBJECT -> result.set(fieldName, JsonUtils.emptyNode());
                    case ARRAY, INT_VECTOR, FLOAT_VECTOR, BYTE_VECTOR -> result.set(fieldName,
                        JsonUtils.getObjectMapper().createArrayNode());
                    default -> result.set(fieldName, JsonUtils.getObjectMapper().nullNode());
                }

            }
        });
    }


    /**
     * 顺序执行算子
     *
     * @param messageContext 数据处理上下文
     * @param operators      算子列表
     * @param input          输入数据（JSON 格式）
     * @param stopWatch      计时器
     * @return 处理后的输出数据（JSON 格式）
     */
    public JsonNode execute(MessageContext messageContext, List<ExecuteOperator> operators, JsonNode input,
        StreamEngineStopWatch stopWatch) {
        JsonNode result = input;
        // 设置上下文
        ThreadLocalUtil.setContext(result);
        for (ExecuteOperator executeOperator : operators) {
            // 这里应该记录每个算子执行的日志记录
            Operator operator = executeOperator.getOperator();
            stopWatch.start(operator.getName());
            TaskInfo taskInfo = new TaskInfo(operator.getId(), operator.getName(), operator.getAbilityId(),
                operator.getStorageId());
            try {
                ThreadLocalUtil.clearOperatorContext();
                //执行规则引擎脚本
                taskInfo.setCondition(executeOperator.getConditionStr());
                if (AviatorService.executeAviatorScript(result, executeOperator.getScript())) {
                    taskInfo.setConditionResult(true);
                    taskInfo.setInput(JsonUtils.toJsonString(
                        operator.getInputBind().getParamsMap(result, operator.getAbility().getInputSchema())));

                    populateAbilityCenterProperties(operator.getAbility());
                    //执行算子；将结果作为下一个算子的输入
                    result = operator.execute(result, timeoutMinutes);

                    taskInfo.setOutput(JsonUtils.toJsonString(operator.getOutputBind().getResultMap(result,
                        operator.getOutputFields())));
                }

                if (ThreadLocalUtil.getErrorMsg() != null) {
                    taskInfo.setIsError(1);
                    taskInfo.setErrorMessage(ThreadLocalUtil.getErrorMsg());
                }
                if (Boolean.TRUE.equals(ThreadLocalUtil.getStopExecuteValue())) {
                    throw new PipelineInterruptedException("停止算子执行");
                }
                ThreadLocalUtil.clearOperatorContext();
                ThreadLocalUtil.setContext(result);
            } catch (Throwable e) {
                if (e instanceof PipelineInterruptedException) {
                    log.warn("执行了停止算子，停止后续流程执行！建模id：{}，数据id：{}",
                        messageContext.getDataModelId(), messageContext.getRecordId());
                    break;
                } else {
                    log.error("算子执行错误！建模id：{}，数据id：{}，算子名称：{}",
                        messageContext.getDataModelId(), messageContext.getRecordId(), operator.getName(), e);
                    taskInfo.setIsError(1);
                    taskInfo.setErrorMessage(e.getMessage());
                }
            } finally {
                if (stopWatch.isRunning()) {
                    stopWatch.stop(taskInfo);
                }
            }

        }
        ThreadLocalUtil.clearContext();
        return result;
    }

    private void populateAbilityCenterProperties(Ability ability) {
        ability.setAbilityCenterProperties(abilityCenterGatewayProperties.to());
    }

    private DataProcessResult createTracerLog(MessageContext messageContext, StreamEngineStopWatch stopWatch,
        boolean isFullTracerEnabled, Integer totalOperatorCount, JsonNode input, JsonNode result) {
        List<DataProcessTrace> traces = new ArrayList<>();
        // 如果链路日志开启，则记录每个算子的执行情况；若执行有错误则强制开启链路日志
        if (isFullTracerEnabled || stopWatch.isError().equals(1)) {
            TaskInfo[] taskInfos = stopWatch.getTaskInfo();
            for (int i = 1; i <= taskInfos.length; i++) {
                DataProcessTrace trace = MessageContext.toTrace(messageContext, taskInfos[i - 1], i);
                traces.add(trace);
            }
        }
        // 记录全链路日志
        DataProcessRecord record = MessageContext.toRecord(messageContext, stopWatch, totalOperatorCount, input);
        return new DataProcessResult(record, traces, result);
    }

    /**
     * 发送日志到kafka 老的发送日志逻辑只能接收{@link TracerData}类型的数据，因此额外转换一次
     *
     * @param traces 日志
     * @see com.trs.ai.ty.base.monitor.send.MonitorSender#sendMonitor(Object)
     */
    private void send(List<DataProcessTrace> traces) {
        //TODO 重构推送日志逻辑
        List<TracerData> data = traces.stream().map(DataProcessTrace::toTracerData)
            .collect(Collectors.toCollection(ArrayList::new));
        monitorBufferService.push(data);
    }

    /**
     * 发送日志到kafka
     *
     * @param processRecord 日志
     * @see com.trs.ai.ty.base.monitor.send.MonitorSender#sendMonitor(Object)
     */
    private void send(DataProcessRecord processRecord) {
        monitorBufferService.push(processRecord.toRecord());
    }
}