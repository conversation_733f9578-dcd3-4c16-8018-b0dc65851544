package com.trs.moye.stream.engine.controller;

import com.trs.moye.ability.LocalAbilityScanner;
import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.entity.AbilityExecuteParams;
import com.trs.moye.ability.invoker.AbilityInvoker;
import com.trs.moye.ability.invoker.AbilityInvokerFactory;
import com.trs.moye.ability.request.RetryOperatorRequest;
import com.trs.moye.ability.response.RetryOperatorResponse;
import com.trs.moye.base.common.response.ResponseMessage;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.stream.engine.dao.AbilityMapper;
import com.trs.moye.stream.engine.properties.AbilityCenterGatewayProperties;
import com.trs.moye.stream.engine.service.MsgProcessService;
import java.util.List;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 能力接口
 */
@Slf4j
@RestController
@RequestMapping("/ability")
@RequiredArgsConstructor
public class AbilityController {

    @Resource
    private AbilityCenterGatewayProperties abilityCenterGatewayProperties;

    @Resource
    private AbilityMapper abilityMapper;

    @Resource
    private MsgProcessService msgProcessService;

    @Value("${timeout.minutes:60}")
    private Integer timeoutMinutes;

    /**
     * 获取基础能力
     *
     * @return 基础能力列表
     */
    @GetMapping("/base")
    public List<Ability> getBaseAbility() {
        return LocalAbilityScanner.scanPackage("com.trs.moye.ability.base");
    }

    /**
     * 能力测试
     *
     * @param params 能力执行参数
     * @return 能力执行结果
     */
    @PostMapping(value = "/execute")
    public ResponseMessage execute(@RequestBody AbilityExecuteParams params) {
        try {
            Ability ability = abilityMapper.selectById(params.getAbilityId());
            AbilityInvoker invoker = AbilityInvokerFactory.getAbilityInvoker(ability.getType(), timeoutMinutes);
            ability.setAbilityCenterProperties(abilityCenterGatewayProperties.to());
            Object invoke = invoker.invoke(ability, params.getInput(), params.getInputBind());
            return ResponseMessage.ok(invoke);
        } catch (Throwable e) {
            log.error("能力[{}]执行失败: ", params.getAbilityId(), e);
            return ResponseMessage.error(e.getMessage());
        }
    }

    /**
     * 验证算子
     *
     * @param ruleRequest 规则请求
     * @return {@link String }
     */
    @PostMapping("/operator/retry")
    public ResponseMessage retryOperator(@RequestBody RetryOperatorRequest ruleRequest) {
        try {
            RetryOperatorResponse result = msgProcessService.retryOperator(ruleRequest.getDataModelId(),
                ruleRequest.getRecordId(), ruleRequest.getOperatorId(),
                JsonUtils.mapToJsonNode(ruleRequest.getParams()));
            return ResponseMessage.ok(result);
        } catch (Exception e) {
            log.error("验证数据建模：{}，记录id：{}，算子id：{}失败: ", ruleRequest.getDataModelId(),
                ruleRequest.getRecordId(), ruleRequest.getOperatorId(), e);
            return ResponseMessage.error(e.getMessage());
        }
    }
}
