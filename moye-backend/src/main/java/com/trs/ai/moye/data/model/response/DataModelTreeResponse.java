package com.trs.ai.moye.data.model.response;

import com.trs.ai.moye.data.model.enums.CategoryTreeNodeType;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.connection.enums.DataSourceCategory;
import com.trs.moye.base.data.execute.ExecuteModeEnum;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.enums.CreateModeEnum;
import com.trs.moye.base.data.model.enums.ModelExecuteStatus;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 算子库树形结构
 *
 * <AUTHOR>
 * @since 2024/5/16 14:42
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataModelTreeResponse extends CategoryTreeResponse {

    /**
     * 调度类型 1-定时
     */
    private ExecuteModeEnum dispatchType;

    /**
     * 最近更新时间
     */
    private LocalDateTime recentUpdateTime;

    /**
     * 任务启动状态
     */
    private ModelExecuteStatus startStatus;

    /**
     * 数据源类型
     */
    private ConnectionType dataSourceType;
    /**
     * 数据源大类
     */
    private DataSourceCategory dataSourceCategory;

    /**
     * 建模方式
     */
    private CreateModeEnum dataModelType;

    /**
     * 层级
     */
    private ModelLayer modelLayer;

    /**
     * 未读异常数
     */
    private Integer unreadErrorCount = 0;
    /**
     * 是否已发布到MCP
     */
    private Boolean isMcpPublished;



    public DataModelTreeResponse(DataModel model) {
        this.setNodeType(CategoryTreeNodeType.MODEL);
        this.setEnName(model.getEnName());
        this.setId(model.getId());
        this.setPid(model.getLayer().ordinal());
        this.setName(model.getZhName());
        this.startStatus = model.getExecuteStatus();
        this.dataModelType = model.getCreateMode();
        this.modelLayer = model.getLayer();
        this.isMcpPublished = model.getIsMcpPublished();
    }
}
