package com.trs.ai.moye.data.model.service;

import com.trs.ai.moye.data.model.dto.ConnectionStoragePoints;
import com.trs.ai.moye.data.model.entity.BatchTaskRecord;
import com.trs.ai.moye.data.model.entity.BatchTaskTracer;
import com.trs.ai.moye.data.model.request.BatchProcessDataListRequest;
import com.trs.ai.moye.data.model.request.BatchTaskLogRequest;
import com.trs.ai.moye.data.model.request.DwdAddRequest;
import com.trs.ai.moye.data.model.request.DwdStartRequest;
import com.trs.ai.moye.data.model.request.SaveArrangeInfoRequest;
import com.trs.ai.moye.data.model.request.fields.CheckExistedTableFieldsRequest;
import com.trs.ai.moye.data.model.response.BatchTaskLogResponse;
import com.trs.ai.moye.data.model.response.BatchTaskRecordResponse;
import com.trs.ai.moye.data.model.response.CheckExistedTableFieldsResponse;
import com.trs.ai.moye.data.model.response.DwdAddResponse;
import com.trs.ai.moye.data.model.response.DwdExecuteScheduleResponse;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.data.connection.enums.SourceStructureType;
import com.trs.moye.base.data.model.entity.DataModel;
import io.minio.errors.ErrorResponseException;
import io.minio.errors.InsufficientDataException;
import io.minio.errors.InternalException;
import io.minio.errors.InvalidResponseException;
import io.minio.errors.ServerException;
import io.minio.errors.XmlParserException;
import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.List;

/**
 * 要素库实现类接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/12 10:39
 **/
public interface DwdModelService {

    /**
     * 要素库新增
     *
     * @param request 前端请求
     * @return {@link DwdAddResponse}
     * <AUTHOR>
     * @since 2024/9/27 18:13
     */
    DwdAddResponse addModel(DwdAddRequest request);


    /**
     * 添加建模的存储信息
     *
     * @param connectionStoragePointsList 连接存储点列表
     * @param dataModel                   数据建模
     * <AUTHOR>
     * @since 2024/12/2 14:36
     */
    void addStorageInfo(List<ConnectionStoragePoints> connectionStoragePointsList, DataModel dataModel);

    /**
     * 创建数据来源
     *
     * @param dataModelId    数据建模id
     * @param sourceModelIds 源模型 ID
     * <AUTHOR>
     * @since 2025/05/19 16:52:30
     */
    void createDataSources(Integer dataModelId, List<Integer> sourceModelIds);

    /**
     * 从 元数据标准 创建建模字段
     *
     * @param type               连接数据库类型
     * @param dataModelId        数据建模id
     * @param metaDataStandardId 元数据标准id
     * <AUTHOR>
     * @since 2024/12/20 15:11:22
     */
    void createModelFieldsFromMetaDataStandard(Integer dataModelId, Integer metaDataStandardId,
        SourceStructureType type);

    /**
     * 启动要素库任务
     *
     * @param id              贴源库id
     * @param dwdStartRequest spark配置
     */
    void startTask(Integer id, DwdStartRequest dwdStartRequest);

    /**
     * 停止要素库任务
     *
     * @param id 贴源库id
     */
    void stopTask(Integer id);

    /**
     * 暂停要素库任务
     *
     * @param id 贴源库id
     */
    void pauseTask(Integer id);


    /**
     * 获取批处理处理数据列表
     *
     * @param request 前端请求
     * @return {@link BatchTaskRecord}
     * <AUTHOR>
     * @since 2024/10/17 14:23
     */
    PageResponse<BatchTaskRecordResponse> getBatchProcessDataList(BatchProcessDataListRequest request);

    /**
     * 获取批任务监控记录链路列表
     *
     * @param executeId 执行id
     * @return 链路列表
     */
    List<BatchTaskTracer> getBatchTaskRecordTracerList(String executeId);

    /**
     * 获取批处理日志列表
     *
     * @param request 前端请求
     * @return {@link BatchTaskLogResponse}
     * <AUTHOR>
     * @since 2024/10/29 10:59
     */
    List<BatchTaskLogResponse> getBatchLogs(BatchTaskLogRequest request);

    /**
     * 获取批处理日志对象名
     *
     * @param request 前端请求
     * @return 日志对象名列表
     * @throws ServerException           服务端异常
     * @throws InsufficientDataException 数据不足异常
     * @throws ErrorResponseException    错误响应异常
     * @throws IOException               IO异常
     * @throws NoSuchAlgorithmException  无算法异常
     * @throws InvalidKeyException       无效密钥异常
     * @throws InvalidResponseException  无效响应异常
     * @throws XmlParserException        XML解析异常
     * @throws InternalException         内部异常
     */
    List<String> getBatchLogObjects(BatchTaskLogRequest request)
        throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException;

    /**
     * 通过文件路径获取日志文件
     *
     * @param path 文件路径
     * @return {@link String}
     * <AUTHOR>
     * @since 2024/10/29 11:11
     */
    String getLogFileByLogPath(String path);

    /**
     * 更新数据来源
     *
     * @param dataModelId   数据建模id
     * @param dataSourceIds 数据来源id
     */
    void updateDataSource(Integer dataModelId, List<Integer> dataSourceIds);

    /**
     * 检查已存在表字段
     *
     * @param request 请求
     * @return {@link CheckExistedTableFieldsResponse }
     * <AUTHOR>
     * @since 2024/12/11 17:21:56
     */
    CheckExistedTableFieldsResponse checkExistedTableFields(CheckExistedTableFieldsRequest request);

    /**
     * 获取要素库执行计划信息
     *
     * @param dataModelId 数据型id
     * @return {@link DwdExecuteScheduleResponse }
     * <AUTHOR>
     * @since 2025/02/21 17:51:44
     */
    DwdExecuteScheduleResponse getModelExecuteScheduleInfo(Integer dataModelId);

    /**
     * 从元数据标准同步字段到数据建模
     *
     * @param dataModelId        数据建模id
     * @param metaDataStandardId 元数据标准id
     * <AUTHOR>
     * @since 2025/08/01 17:00:58
     */
    void syncFieldsFromMetaDataStandard(Integer dataModelId, Integer metaDataStandardId);

    /**
     * 保存数据治理相关信息
     *
     * @param dataModelId 数据建模id
     * @param request     前端请求
     * <AUTHOR>
     * @since 2025/08/01 17:00:56
     */
    void saveArrangeInfo(Integer dataModelId, SaveArrangeInfoRequest request);
}
