package com.trs.ai.moye.monitor.service.impl;

import static com.trs.ai.moye.monitor.controller.DataStatisticsController.DAILY_STATISTICS;
import static com.trs.ai.moye.monitor.controller.DataStatisticsController.ON_TIME_STATISTICS;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.ai.moye.data.model.dao.BatchTaskRecordMapper;
import com.trs.ai.moye.homepage.dao.HomePageDwdStatisticsMapper;
import com.trs.ai.moye.homepage.entity.HomePageDwdStatistics;
import com.trs.ai.moye.homepage.enums.StatisticsDwdType;
import com.trs.ai.moye.monitor.entity.BatchTaskListAllResponse;
import com.trs.ai.moye.monitor.entity.BatchTaskListRequest;
import com.trs.ai.moye.monitor.entity.BatchTaskListResponse;
import com.trs.ai.moye.monitor.entity.ProcessTimeRange;
import com.trs.ai.moye.monitor.request.HomePageDwdTrendRequest;
import com.trs.ai.moye.monitor.response.statistics.BatchTaskExecuteResponse;
import com.trs.ai.moye.monitor.response.statistics.ProcessSegmentationStatistics;
import com.trs.ai.moye.monitor.response.statistics.StreamTaskBarChart;
import com.trs.ai.moye.monitor.response.statistics.StreamTaskHandleResponse;
import com.trs.ai.moye.monitor.service.DwdMonitorService;
import com.trs.ai.moye.monitor.utils.StatisticalTimeRangeUtils;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * 要素库统计service
 *
 * <AUTHOR>
 * @since 2025/5/8
 */
@Service
public class DwdMonitorServiceImpl implements DwdMonitorService {

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private HomePageDwdStatisticsMapper homePageDwdStatisticsMapper;

    @Resource
    private BatchTaskRecordMapper batchTaskRecordMapper;

    @Override
    public BatchTaskExecuteResponse getBatchTaskExecuteResponse(HomePageDwdTrendRequest request) {
        List<Integer> dataModelIds = dataModelMapper.selectDwdByCategoryId(request.getBusinessCategoryId());
        ProcessTimeRange timeRange =
            StatisticalTimeRangeUtils.getTimeRange(request.getType(), request.getTimeRangeParams());
        List<HomePageDwdStatistics> homePageDwdStatistics = homePageDwdStatisticsMapper.selectAll("BATCH", dataModelIds,
            timeRange.getStartTime(), timeRange.getEndTime());
        long totalExecuteCount = homePageDwdStatistics.stream()
            .mapToLong(HomePageDwdStatistics::getTotalCount)
            .sum();
        long totalSuccessCount = homePageDwdStatistics.stream()
            .mapToLong(HomePageDwdStatistics::getSuccessCount)
            .sum();
        long totalFailCount = homePageDwdStatistics.stream()
            .mapToLong(HomePageDwdStatistics::getFailCount)
            .sum();
        BatchTaskExecuteResponse response = new BatchTaskExecuteResponse();
        response.setExecuteFailCount(totalFailCount);
        response.setExecuteSuccessCount(totalSuccessCount);
        response.setExecuteTotalCount(totalExecuteCount);
        // 折线图
        List<ProcessSegmentationStatistics> successLine;
        List<ProcessSegmentationStatistics> failLine;
        if (DAILY_STATISTICS.equals(request.getType())) {
            //按日统计
            successLine = homePageDwdStatisticsMapper.selectSuccessLineByDay(timeRange.getStartTime(),
                timeRange.getEndTime(),
                StatisticsDwdType.BATCH, dataModelIds);
            failLine = homePageDwdStatisticsMapper.selectFailLineByDay(timeRange.getStartTime(), timeRange.getEndTime(),
                StatisticsDwdType.BATCH, dataModelIds);
        } else if (ON_TIME_STATISTICS.equals(request.getType())) {
            //按时分统计
            successLine = homePageDwdStatisticsMapper.selectSuccessLineByHour(timeRange.getStartTime(),
                timeRange.getEndTime(), StatisticsDwdType.BATCH, dataModelIds);
            failLine = homePageDwdStatisticsMapper.selectBatchFailLineByHour(StatisticsDwdType.BATCH, dataModelIds);
        } else {
            throw new BizException("目前只支持按日统计或者按时分统计!");
        }
        response.setSuccessLine(successLine);
        response.setFailLine(failLine);
        return response;
    }

    @Override
    public StreamTaskHandleResponse getStreamTaskHandleResponse(HomePageDwdTrendRequest request) {
        List<Integer> dataModelIds = dataModelMapper.selectDwdByCategoryId(request.getBusinessCategoryId());
        ProcessTimeRange timeRange =
            StatisticalTimeRangeUtils.getTimeRange(request.getType(), request.getTimeRangeParams());
        List<HomePageDwdStatistics> homePageDwdStatistics = homePageDwdStatisticsMapper.selectAll("STREAM",
            dataModelIds, timeRange.getStartTime(), timeRange.getEndTime());
        long totalExecuteCount = homePageDwdStatistics.stream()
            .mapToLong(HomePageDwdStatistics::getSuccessCount) //这里取获取处理成功的量还是全量?
            .sum();
        StreamTaskHandleResponse response = new StreamTaskHandleResponse();
        response.setTotalCount(totalExecuteCount);
        //折线图
        List<ProcessSegmentationStatistics> line;
        if (DAILY_STATISTICS.equals(request.getType())) {
            //按日统计
            line = homePageDwdStatisticsMapper.selectAllLineByDay(timeRange.getStartTime(), timeRange.getEndTime(),
                StatisticsDwdType.STREAM, dataModelIds);
        } else if (ON_TIME_STATISTICS.equals(request.getType())) {
            //按时分统计
            line = homePageDwdStatisticsMapper.selectStreamLineByHour(StatisticsDwdType.BATCH, dataModelIds);
        } else {
            throw new BizException("目前只支持按日统计或者按时分统计!");
        }
        response.setLine(line);
        return response;
    }

    @Override
    public List<StreamTaskBarChart> getStreamTaskBarChart(HomePageDwdTrendRequest request) {
        List<Integer> dataModelIds = dataModelMapper.selectDwdByCategoryId(request.getBusinessCategoryId());
        ProcessTimeRange timeRange =
            StatisticalTimeRangeUtils.getTimeRange(request.getType(), request.getTimeRangeParams());
        List<StreamTaskBarChart> response;
        if (DAILY_STATISTICS.equals(request.getType())) {
            //按日统计
            response = homePageDwdStatisticsMapper.selectStreamBarChartByDay(timeRange.getStartTime(),
                timeRange.getEndTime(),
                StatisticsDwdType.STREAM, dataModelIds, request.getTop());
        } else if (ON_TIME_STATISTICS.equals(request.getType())) {
            //按时分统计
            response = homePageDwdStatisticsMapper.selectStreamBarChartByHour(StatisticsDwdType.BATCH, dataModelIds,
                request.getTop());
        } else {
            throw new BizException("目前只支持按日统计或者按时分统计!");
        }
        return response;
    }

    @Override
    public StreamTaskHandleResponse getStorageLine(HomePageDwdTrendRequest request) {
        List<Integer> dataModelIds = dataModelMapper.selectDwdByCategoryId(request.getBusinessCategoryId());
        ProcessTimeRange timeRange =
            StatisticalTimeRangeUtils.getTimeRange(request.getType(), request.getTimeRangeParams());
        Long totalCount;
        List<ProcessSegmentationStatistics> line;
        if (DAILY_STATISTICS.equals(request.getType())) {
            //按日统计
            line = homePageDwdStatisticsMapper.selectAllLineByDay(timeRange.getStartTime(), timeRange.getEndTime(),
                StatisticsDwdType.STORAGE, dataModelIds);
            totalCount = homePageDwdStatisticsMapper.selectTotalStorageByDay(timeRange.getStartTime(),
                timeRange.getEndTime(),
                StatisticsDwdType.STORAGE, dataModelIds);
        } else if (ON_TIME_STATISTICS.equals(request.getType())) {
            //按时分统计
            totalCount = homePageDwdStatisticsMapper.selectTotalStorageByHour(dataModelIds);
            line = homePageDwdStatisticsMapper.selectStorageLineByHour(dataModelIds);
        } else {
            throw new BizException("目前只支持按日统计或者按时分统计!");
        }
        StreamTaskHandleResponse response = new StreamTaskHandleResponse();
        response.setLine(line);
        response.setTotalCount(totalCount);
        return response;
    }

    @Override
    public Long operatorExecuteCount() {
        return homePageDwdStatisticsMapper.selectOperatorExecuteCount();
    }

    @Override
    public BatchTaskListAllResponse getBatchTaskListResponse(BatchTaskListRequest request) {
        // 将驼峰转为下划线
        handleRequestParamsToSnake(request);
        BatchTaskListAllResponse response = new BatchTaskListAllResponse();
        String startTime = null;
        String endTime = null;
        if (Objects.nonNull(request.getTimeRangeParams())) {
            ProcessTimeRange timeRange =
                StatisticalTimeRangeUtils.getTimeRange(request.getTimeRangeParams());
            startTime = timeRange.getStartTime();
            endTime = timeRange.getEndTime();
        }
        Page<BatchTaskListResponse> page = batchTaskRecordMapper.getBatchTaskListResponse(request.getSearchParams(),
            request.getSortParams(), request.getPageParams().toPage(), request.getLastStatus(), startTime, endTime);
        if (page == null || CollectionUtils.isEmpty(page.getRecords())) {
            return response;
        }
        page.getRecords().forEach(item -> {
            if (Objects.nonNull(item.getLastDurationTime()) && item.getLastDurationTime() < 0) {
                item.setLastDurationTime(0);
            }
        });

        response.setPageResponse(PageResponse.of(page));
        setResponseExecuteCount(startTime, endTime, response);
        return response;
    }

    private void setResponseExecuteCount(String startTime, String endTime, BatchTaskListAllResponse response) {
        List<HomePageDwdStatistics> homePageDwdStatistics = homePageDwdStatisticsMapper.selectAll("BATCH", null,
            startTime, endTime);
        long totalExecuteCount = homePageDwdStatistics.stream()
            .mapToLong(HomePageDwdStatistics::getTotalCount)
            .sum();
        long totalSuccessCount = homePageDwdStatistics.stream()
            .mapToLong(HomePageDwdStatistics::getSuccessCount)
            .sum();
        long totalFailCount = homePageDwdStatistics.stream()
            .mapToLong(HomePageDwdStatistics::getFailCount)
            .sum();
        response.setExecuteFailCount(totalFailCount);
        response.setExecuteSuccessCount(totalSuccessCount);
        response.setExecuteTotalCount(totalExecuteCount);
    }

    private void handleRequestParamsToSnake(BatchTaskListRequest request) {
        Optional.ofNullable(request.getSortParams())
            .ifPresent(sortParams -> {
                String field = sortParams.getField();
                sortParams.setField(camelToSnake(field));
            });
        Optional.ofNullable(request.getSearchParams())
            .ifPresent(searchParams -> searchParams.getFields()
                .replaceAll(field -> field != null ? camelToSnake(field) : null));
    }

    private String camelToSnake(String camelCase) {
        if (camelCase == null || camelCase.isEmpty()) {
            return camelCase;
        }

        StringBuilder result = new StringBuilder();
        result.append(Character.toLowerCase(camelCase.charAt(0)));

        for (int i = 1; i < camelCase.length(); i++) {
            char currentChar = camelCase.charAt(i);
            if (Character.isUpperCase(currentChar)) {
                result.append('_').append(Character.toLowerCase(currentChar));
            } else {
                result.append(currentChar);
            }
        }

        return result.toString();
    }
}
