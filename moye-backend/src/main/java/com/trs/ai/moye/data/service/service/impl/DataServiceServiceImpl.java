package com.trs.ai.moye.data.service.service.impl;

import static com.trs.ai.moye.data.service.service.impl.DataServiceAbilityCenterServiceImpl.AUTHORIZATION_STR;

import com.trs.ai.moye.common.response.TreeAddResponse;
import com.trs.ai.moye.common.utils.BeanUtil;
import com.trs.ai.moye.data.connection.response.DataStorageTreeResponse;
import com.trs.ai.moye.data.model.response.ModelBasicInfoResponse;
import com.trs.ai.moye.data.model.service.DataModelService;
import com.trs.ai.moye.data.service.config.AbilityCenterProperties;
import com.trs.ai.moye.data.service.dao.DataServiceCategoryMapper;
import com.trs.ai.moye.data.service.dao.DataServiceConfigMapper;
import com.trs.ai.moye.data.service.dao.DataServiceMapper;
import com.trs.ai.moye.data.service.dto.DataServiceDto;
import com.trs.ai.moye.data.service.entity.DataService;
import com.trs.ai.moye.data.service.entity.DataServiceCategory;
import com.trs.ai.moye.data.service.entity.DataServiceCategoryTree;
import com.trs.ai.moye.data.service.entity.DataServiceConfig;
import com.trs.ai.moye.data.service.entity.DataServiceField;
import com.trs.ai.moye.data.service.entity.params.DataServiceConfigParams;
import com.trs.ai.moye.data.service.entity.params.ServiceQueryParams;
import com.trs.ai.moye.data.service.entity.query.Condition;
import com.trs.ai.moye.data.service.entity.query.ValueObject;
import com.trs.ai.moye.data.service.enums.DataServiceConditionType;
import com.trs.ai.moye.data.service.enums.ServiceCategoryTreeNode;
import com.trs.ai.moye.data.service.enums.ServiceHealthStatus;
import com.trs.ai.moye.data.service.enums.ServicePublishStatus;
import com.trs.ai.moye.data.service.enums.ability.AbilityParamDataType;
import com.trs.ai.moye.data.service.enums.ability.AbilityParamEncrypt;
import com.trs.ai.moye.data.service.enums.ability.AbilityParamLocation;
import com.trs.ai.moye.data.service.enums.ability.AbilityParamRequire;
import com.trs.ai.moye.data.service.processer.code.CodeModeProcessor;
import com.trs.ai.moye.data.service.processer.process.DataServiceProcessorContext;
import com.trs.ai.moye.data.service.request.CheckNameDataServiceRequest;
import com.trs.ai.moye.data.service.request.CodeBlockRequest;
import com.trs.ai.moye.data.service.request.DataServiceCloneRequest;
import com.trs.ai.moye.data.service.request.DataServicePreviewRequest;
import com.trs.ai.moye.data.service.request.DataServiceRequest;
import com.trs.ai.moye.data.service.request.ability.DataServicePublishRequest;
import com.trs.ai.moye.data.service.request.ability.PowerParameter;
import com.trs.ai.moye.data.service.request.ability.ReqBody;
import com.trs.ai.moye.data.service.response.DataServiceCategoryTreeResponse;
import com.trs.ai.moye.data.service.response.DataServiceDefaultValueResponse;
import com.trs.ai.moye.data.service.response.DataServiceResponse;
import com.trs.ai.moye.data.service.response.ability.AbilityCheckUnpublishResponse;
import com.trs.ai.moye.data.service.response.ability.AbilityMsgResponse;
import com.trs.ai.moye.data.service.service.DataServiceAbilityCenterService;
import com.trs.ai.moye.data.service.service.DataServiceService;
import com.trs.ai.moye.permission.service.DynamicUserNameService;
import com.trs.ai.moye.storageengine.request.StorageEngineSearchRequest;
import com.trs.ai.moye.storageengine.response.StorageSearchResponse;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.request.PageParams;
import com.trs.moye.base.common.response.IdNameResponse;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.utils.AssertUtils;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.common.utils.RandomCodeUtil;
import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.connection.enums.DataSourceCategory;
import com.trs.moye.base.data.model.enums.CreateTableStatus;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 数据服务实现
 *
 * <AUTHOR>
 * @since 2024/09/25 16:41:43
 */
@Slf4j
@Service
public class DataServiceServiceImpl implements DataServiceService {

    private static final String REQUEST_PARAM = "requestParam";
    private static final String MOCK_HEADER = "Basic YWRtaW46dHJzYWRtaW5AMTIzNA==";
    @Resource
    private DataServiceCategoryMapper dataServiceCategoryMapper;
    @Resource
    private DataServiceMapper dataServiceMapper;
    @Resource
    private DataServiceConfigMapper dataServiceConfigMapper;
    @Resource
    private DataServiceProcessorContext dataServiceProcessorContext;
    @Resource
    private DataServiceAbilityCenterService dataServiceAbilityCenterService;
    @Resource
    private AbilityCenterProperties abilityCenterProperties;
    @Resource
    private DataStorageMapper dataStorageMapper;
    @Resource
    private DataConnectionMapper dataConnectionMapper;
    @Resource
    private DataModelService dataModelService;

    /**
     * 获取分类树
     *
     * @return {@link List }<{@link DataServiceCategoryTree }>
     * <AUTHOR>
     * @since 2024/09/25 18:00:04
     */
    @Override
    public List<DataServiceCategoryTree> getCategoryTree() {
        List<DataServiceCategoryTree> categoryTree = dataServiceCategoryMapper.selectCategoryTree();
        List<DataService> dataServiceList = dataServiceMapper.selectList(null);
        buildTree(categoryTree, dataServiceList);
        return categoryTree;
    }

    /**
     * 检查服务名称重复
     *
     * @param request 数据服务
     * @return boolean
     * <AUTHOR>
     * @since 2024/09/25 18:00:13
     */
    @Override
    public boolean checkServiceName(CheckNameDataServiceRequest request) {
        return dataServiceMapper.checkName(request);
    }

    /**
     * 添加数据服务
     *
     * @param request 请求
     * <AUTHOR>
     * @since 2024/09/25 18:00:23
     */
    @Override
    @Transactional
    public TreeAddResponse addDataService(DataServiceRequest request) {
        request.setCode(getDataServiceCode());
        request.setPublishStatus(ServicePublishStatus.UNRELEASED);
        request.setHealthStatus(ServiceHealthStatus.UNCHECKED);
        dataServiceMapper.insert(request);
        request.getDataServiceConfig().setDataServiceId(request.getId());
        dataServiceConfigMapper.insert(request.getDataServiceConfig());
        return new TreeAddResponse(ServiceCategoryTreeNode.SERVICE, request.getId());
    }

    /**
     * 获取全局唯一code
     *
     * @return java.lang.String
     */
    public String getDataServiceCode() {
        while (true) {
            String code = RandomCodeUtil.buildDataServiceCode();
            DataService dataService = dataServiceMapper.selectByCode(code);
            if (Objects.isNull(dataService) || Objects.isNull(dataService.getId())) {
                return code;
            }
        }
    }


    /**
     * 更新数据服务
     *
     * @param request 请求
     * <AUTHOR>
     * @since 2024/09/27 10:52:46
     */
    @Override
    @Transactional
    public void updateDataService(DataServiceRequest request) {
        dataServiceMapper.updateById(request);
        DataServiceConfig config = request.getDataServiceConfig();
        if (Objects.nonNull(config)) {
            config.setDataServiceId(request.getId());
            if (Objects.nonNull(config.getId())) {
                dataServiceConfigMapper.updateById(config);
            } else if (Objects.nonNull(dataServiceConfigMapper.selectOneByDataServiceId(request.getId()))) {
                dataServiceConfigMapper.updateByDataServiceId(config);
            } else {
                dataServiceConfigMapper.insert(config);
            }
        }

    }

    @Override
    public DataServiceResponse getDetail(Integer id) {
        DynamicUserNameService dynamicUserNameService = BeanUtil.getBean(DynamicUserNameService.class);
        DataServiceDto dataServiceDto = dataServiceMapper.selectDtoById(id);
        return new DataServiceResponse(dataServiceDto, dynamicUserNameService);
    }

    @Override
    @Transactional
    public void deleteDataService(List<Integer> ids) {
        dataServiceMapper.deleteByIds(ids);
        for (Integer id : ids) {
            dataServiceConfigMapper.deleteByDataServiceId(id);
        }
    }

    @Override
    @Transactional
    public boolean publish(Integer id) {
        DataServiceDto dataServiceDto = dataServiceMapper.selectDtoById(id);
        // 获取应用ID
        Long appId = dataServiceAbilityCenterService.getAppId();
        DataServicePublishRequest request = createPublishRequest(dataServiceDto, appId);
        AbilityMsgResponse msgResponse = dataServiceAbilityCenterService.publish(request);
        // 从返回结果中获取能力 ID
        String abilityId = extractAbilityIdFromMessage(msgResponse.getMessage());
        dataServiceDto.setAbilityCenterId(Integer.parseInt(abilityId));
        dataServiceDto.setPublishStatus(ServicePublishStatus.RELEASED);
        dataServiceMapper.updateById(dataServiceDto);
        return true;
    }

    @Override
    public AbilityCheckUnpublishResponse checkUnpublish(Integer id) {
        DataServiceDto dataServiceDto = dataServiceMapper.selectDtoById(id);
        Integer abilityCenterId = getAbilityCenterId(dataServiceDto);
        return dataServiceAbilityCenterService.checkUnpublish(abilityCenterId, dataServiceDto.getName());
    }

    @Override
    @Transactional
    public boolean unpublish(Integer id) {
        DataServiceDto dataServiceDto = dataServiceMapper.selectDtoById(id);
        Integer abilityCenterId = getAbilityCenterId(dataServiceDto);
        boolean exist = dataServiceAbilityCenterService.checkAbilityExist(abilityCenterId);
        if (Boolean.FALSE.equals(exist)) {
            log.warn("主键为【{}】的数据服务【{}】,在能力中心不存在,abilityCenterId:{}", id, dataServiceDto.getName(),
                abilityCenterId);
            updatePublishStatus(id, ServicePublishStatus.UNRELEASED);
            return false;
        }
        AbilityMsgResponse msgResponse = dataServiceAbilityCenterService.unpublish(abilityCenterId);

        if (StringUtils.isNotEmpty(msgResponse.getMessage())) {
            // 更新数据库中的服务状态
            updatePublishStatus(id, ServicePublishStatus.REVOKED);
            return true;
        }
        return false;
    }

    @Override
    public boolean republish(Integer id) {
        DataServiceDto dataServiceDto = dataServiceMapper.selectDtoById(id);
        Integer abilityCenterId = getAbilityCenterId(dataServiceDto);
        boolean exist = dataServiceAbilityCenterService.checkAbilityExist(abilityCenterId);
        if (Boolean.FALSE.equals(exist)) {
            return false;
        }
        DataServicePublishRequest request = createPublishRequest(dataServiceDto, 0L);
        dataServiceAbilityCenterService.editAbility(request);
        AbilityMsgResponse msgResponse = dataServiceAbilityCenterService.republish(request);
        if (StringUtils.isNotEmpty(msgResponse.getMessage())) {
            // 更新数据库中的服务状态
            updatePublishStatus(id, ServicePublishStatus.RELEASED);
            return true;
        }
        return false;
    }

    @Override
    public ServicePublishStatus getPublishStatus(Integer id) {
        DataServiceDto dataServiceDto = dataServiceMapper.selectDtoById(id);
        Integer abilityCenterId = dataServiceDto.getAbilityCenterId();
        if (Objects.isNull(abilityCenterId)) {
            return ServicePublishStatus.UNRELEASED;
        }
        Map<Integer, Integer> publishStatus = dataServiceAbilityCenterService.checkPublishStatus(
            Collections.singletonList(abilityCenterId));
        if (publishStatus.containsKey(abilityCenterId)) {
            return ServicePublishStatus.getByCode(publishStatus.get(abilityCenterId))
                .orElseThrow(() -> new BizException("未知的发布状态"));
        }
        // 默认返回未发布
        return ServicePublishStatus.UNRELEASED;
    }

    private Integer getAbilityCenterId(DataServiceDto dataServiceDto) {
        Integer abilityCenterId = dataServiceDto.getAbilityCenterId();
        AssertUtils.notEmpty(abilityCenterId,
            String.format("主键为【%s】的数据服务其对应的能力中心ID不能为空！", dataServiceDto.getId()));
        return abilityCenterId;
    }

    /**
     * 构建分类树
     *
     * @param categoryTree    分类树
     * @param dataServiceList 数据服务列表
     * <AUTHOR>
     * @since 2024/09/25 18:00:31
     */
    private void buildTree(List<DataServiceCategoryTree> categoryTree, List<DataService> dataServiceList) {
        Map<Integer, List<DataService>> dataServiceMap = dataServiceList.stream()
            .collect(Collectors.groupingBy(DataService::getCategoryId));
        for (DataServiceCategoryTree category : categoryTree) {
            if (ObjectUtils.isNotEmpty(category.getChildren())) {
                buildTree(category.getChildren(), dataServiceList);
            } else {
                category.setChildren(new ArrayList<>());
            }
            category.getChildren().addAll(
                dataServiceMap.getOrDefault(category.getId(), Collections.emptyList()).stream()
                    .map(DataServiceCategoryTree::to).toList());
        }
    }


    /**
     * 数据服务预览
     *
     * @param request 请求
     * @return {@link PageResponse}
     */
    @Override
    public PageResponse<Map<String, Object>> preview(DataServicePreviewRequest request) {
        StorageSearchResponse storageSearchResponse;
        PageParams pageParams = request.getPageParams();
        log.info("get data list of request: {}", JsonUtils.toJsonString(request));
        DataStorage storage = dataStorageMapper.selectById(request.getStorageId());
        String tableName = Objects.nonNull(storage) ? storage.getEnName() : "";

        StorageEngineSearchRequest storageEngineSearchRequest = dataServiceProcessorContext.buildSearchRequest(
            pageParams, request.getDataServiceConfig().getParams(), request.getStorageId());
        storageSearchResponse = dataServiceProcessorContext.queryData(request.getCreateMode(),
            request.getConnectionId(), tableName, storageEngineSearchRequest);
        List<Map<String, Object>> items = storageSearchResponse.getItems();
        if (items != null) {
            for (Map<String, Object> item : items) {
                if (item.containsKey("COUNT")) {
                    item.put("count", item.get("COUNT"));
                    item.remove("COUNT");
                }
            }
        }
        return PageResponse.of(items, pageParams.getPageNum(),
            storageSearchResponse.getTotal(), pageParams.getPageSize());
    }

    @Override
    public List<IdNameResponse> getRefConditionList(Integer storageId, Integer serviceId) {
        List<DataServiceDto> dtoList = dataServiceMapper.selectDtoByStorageIds(Collections.singletonList(storageId));
        if (Objects.isNull(serviceId)) {
            return dtoList.stream().map(e -> new IdNameResponse(e.getId(), e.getName())).toList();
        } else {
            return dtoList.stream().filter(e -> !e.getId().equals(serviceId))
                .filter(e -> canReference(e.getCondition(), serviceId))
                .map(e -> new IdNameResponse(e.getId(), e.getName())).toList();
        }
    }

    @Override
    public String getTips(ConnectionType dbType) throws BizException {
        return CodeModeProcessor.getTips(dbType);
    }

    @Override
    public List<String> analyseCode(CodeBlockRequest request) throws BizException {
        return CodeModeProcessor.analyseCode(request);
    }

    private boolean canReference(List<Condition> conditions, Integer serviceId) {
        if (ObjectUtils.isEmpty(conditions)) {
            return true;
        }
        return checkAllConditions(conditions, serviceId);
    }

    private boolean checkAllConditions(List<Condition> conditions, Integer serviceId) {
        // 递归检查所有条件，如果有引用的规则，检查引用的规则
        if (ObjectUtils.isEmpty(conditions)) {
            return true;
        }
        return conditions.stream().allMatch(condition -> {
            if (DataServiceConditionType.REFERENCE.equals(condition.getType())) {
                return condition.getValues().stream().map(ValueObject::getKey).map(Integer::parseInt)
                    .noneMatch(kId -> serviceId.equals(kId) || !checkNestedCondition(kId, serviceId));
            }
            return true;
        });
    }

    private boolean checkNestedCondition(Integer kId, Integer serviceId) {
        DataServiceDto ruleApply = dataServiceMapper.selectDtoById(kId);
        return Objects.isNull(ruleApply) || checkAllConditions(ruleApply.getCondition(), serviceId);
    }

    private DataServicePublishRequest createPublishRequest(DataServiceDto dataServiceDto, Long appId) {
        String invokeServicePath = String.format(abilityCenterProperties.getInvokeServicePath(),
            dataServiceDto.getCode());
        DataServicePublishRequest request = new DataServicePublishRequest(dataServiceDto, invokeServicePath);
        // 构建发布请求
        request.setPowerParameter(generatePowerParameter(dataServiceDto));
        if (Objects.nonNull(appId)) {
            request.setAppId(appId);
        }
        if (Objects.nonNull(dataServiceDto.getAbilityCenterId())) {
            request.setId(Long.valueOf(dataServiceDto.getAbilityCenterId()));
        }
        return request;
    }

    private List<PowerParameter> generatePowerParameter(DataServiceDto dataServiceVO) {
        List<PowerParameter> powerParameters = new ArrayList<>();
        PowerParameter headerParameter = getHeaderParameter();
        powerParameters.add(headerParameter);
        PowerParameter bodyParameter = getBodyParameter(dataServiceVO);
        powerParameters.add(bodyParameter);
        return powerParameters;
    }

    @NotNull
    private PowerParameter getBodyParameter(DataServiceDto dataServiceVO) {
        PowerParameter bodyParameter = new PowerParameter();
        bodyParameter.setParameterLocation(AbilityParamLocation.JSON.getCode());
        bodyParameter.setParameterName(REQUEST_PARAM);
        bodyParameter.setRequired(AbilityParamRequire.OPTIONAL.getCode());
        // 构建请求体
        bodyParameter.setReqBody(generateReqBody(dataServiceVO));
        return bodyParameter;
    }

    @NotNull
    private PowerParameter getHeaderParameter() {
        PowerParameter headerParameter = new PowerParameter();
        headerParameter.setParameterLocation(AbilityParamLocation.HEADERS.getCode());
        headerParameter.setParameterName(AUTHORIZATION_STR);
        headerParameter.setRequired(AbilityParamRequire.REQUIRED.getCode());
        headerParameter.setEncrypted(AbilityParamEncrypt.NOT_ENCRYPT.getCode());
        headerParameter.setDataType(AbilityParamDataType.STRING.getCode());
        headerParameter.setMock(MOCK_HEADER);
        return headerParameter;
    }

    private String generateReqBody(DataServiceDto dataServiceDto) {
        AssertUtils.notEmpty(dataServiceDto.getDataServiceConfig(),
            String.format("主键为【%s】的数据服务其对应的配置不能为空！", dataServiceDto.getId()));
        AssertUtils.notEmpty(dataServiceDto.getDataServiceConfig().getParams(),
            String.format("主键为【%s】的数据服务其对应的配置params参数不能为空！", dataServiceDto.getId()));
        DataServiceConfigParams params = dataServiceDto.getDataServiceConfig().getParams();
        ReqBody reqBody = dataServiceProcessorContext.generateAbilityCenterReqBody(params,
            dataServiceDto.getCreateMode());
        return JsonUtils.toJsonString(reqBody);
    }

    private String extractAbilityIdFromMessage(String message) throws BizException {
        Pattern pattern = Pattern.compile("\\[(\\d+)]");
        Matcher matcher = pattern.matcher(message);
        if (matcher.find()) {
            return matcher.group(1);
        } else {
            throw new BizException("调用能力中心接口-注册能力，结果中获取AbilityCenterId异常！");
        }
    }

    private void updatePublishStatus(Integer id, ServicePublishStatus publishStatus) {
        dataServiceMapper.updatePublishStatusById(id, publishStatus);
    }

    @Override
    public List<DataStorageTreeResponse> getStorageTree() {
        List<DataConnection> dataConnections = dataConnectionMapper.selectAllConnection();
        Map<ConnectionType, List<DataConnection>> groupedByType = dataConnections.stream()
            .filter(dataConnection -> !dataConnection.isSource())
            .collect(Collectors.groupingBy(DataConnection::getConnectionType));
        DataSourceCategory categoryEnum = DataSourceCategory.DATA_BASE;
        Predicate<ConnectionType> filter = connectionType -> connectionType.getCategory() == categoryEnum
            && connectionType.isSupportStorage();

        // 获取过滤后的 type 列表并构建树形结构
        return ConnectionType.filterConnectionType(filter).stream().map(type -> createTypeNode(type, groupedByType))
            .toList();
    }

    @Override
    public void exportDataPreviewCsv(DataServicePreviewRequest request, HttpServletResponse response)
        throws IOException {
        PageParams pageParams = new PageParams();
        pageParams.setPageSize(10000);
        pageParams.setPageNum(1);
        request.setPageParams(pageParams);
        PageResponse<Map<String, Object>> pageResponse = preview(request);
        List<DataServiceField> returnFields = pageResponse.getItems().stream().findFirst()
            .map(e -> e.keySet().stream().map(key -> {
                DataServiceField field = new DataServiceField();
                field.setEnName(key);
                field.setZhName(key);
                return field;
            }).toList()).orElseGet(ArrayList::new);
        if (request.getDataServiceConfig().getParams() instanceof ServiceQueryParams serviceQueryParams) {
            returnFields = serviceQueryParams.getReturnFields();
        }
        dataServiceProcessorContext.doExportData(request.getName(), request.getDataServiceConfig().getType(),
            pageResponse.getItems(), returnFields, response);
    }

    /**
     * 创建连接类型节点（一级节点）
     *
     * @param type          类型
     * @param groupedByType 按类型分组
     * @return {@link DataStorageTreeResponse }
     * <AUTHOR>
     * @since 2024/10/18 16:07:37
     */
    private DataStorageTreeResponse createTypeNode(ConnectionType type,
        Map<ConnectionType, List<DataConnection>> groupedByType) {
        DataStorageTreeResponse typeNode = new DataStorageTreeResponse();
        typeNode.setName(type.getLabel());
        typeNode.setEnName(type.name());

        if (!CodeModeProcessor.SUPPORT_CODE_MODE_CONNECTION_TYPES.contains(type)) {
            return typeNode;
        }
        List<DataStorage> dataStorages =
            ConnectionType.HY_BASE.equals(type) ? dataStorageMapper.selectByConnectionType(type)
                : Collections.emptyList();

        List<DataStorageTreeResponse> connectionNodes = groupedByType.getOrDefault(type, Collections.emptyList())
            .stream().map(dataConnection -> createConnectionNode(dataConnection, dataStorages)).toList();

        typeNode.setChildren(connectionNodes);
        return typeNode;
    }

    /**
     * 创建连接节点（二级节点）
     *
     * @param dataConnection 数据连接
     * @param dataStorages   数据存储
     * @return {@link DataStorageTreeResponse }
     * <AUTHOR>
     * @since 2024/10/18 16:07:57
     */
    private DataStorageTreeResponse createConnectionNode(DataConnection dataConnection,
        List<DataStorage> dataStorages) {
        DataStorageTreeResponse connectionNode = new DataStorageTreeResponse();
        connectionNode.setId(dataConnection.getId());
        connectionNode.setName(dataConnection.getName());
        connectionNode.setEnName(dataConnection.getName());

        // 过滤出当前连接下的 已创建的 存储(三级节点)
        List<DataStorageTreeResponse> children = dataStorages.stream().filter(
            dataStorage -> connectionNode.getId().equals(dataStorage.getConnectionId())
                && CreateTableStatus.SUCCESS.equals(dataStorage.getCreateTableStatus())).map(dataStorage -> {
            DataStorageTreeResponse storageNode = new DataStorageTreeResponse();
            storageNode.setId(dataStorage.getId());
            storageNode.setName(dataStorage.getZhName());
            storageNode.setEnName(dataStorage.getEnName());
            return storageNode;
        }).toList();

        connectionNode.setChildren(children);
        return connectionNode;
    }


    @Override
    public boolean moveMetaDataToCategory(Set<Integer> ids, Integer categoryId) {
        // 校验 业务分类、元数据 的存在性
        DataServiceCategory dataServiceCategory = dataServiceCategoryMapper.selectById(categoryId);
        if (Objects.isNull(dataServiceCategory)) {
            throw new BizException(String.format("未知业务分类[categoryId:%s]", categoryId));
        }
        for (Integer id : ids) {
            DataService dataService = dataServiceMapper.selectById(id);
            if (Objects.isNull(dataService)) {
                throw new BizException(String.format("未知数据服务[服务ID:%s]", id));
            }
        }
        return dataServiceMapper.updateCategoryIdByIds(categoryId, ids);
    }


    @Override
    public DataServiceDefaultValueResponse getDefaultValue(Integer categoryId, Integer dataModelId) {
        ModelBasicInfoResponse modelBasicInfo = dataModelService.getModelBasicInfo(dataModelId);
        DataServiceDefaultValueResponse response = new DataServiceDefaultValueResponse(modelBasicInfo);
        String serviceName = response.getServiceName();
        checkAndBuildNewName(categoryId, serviceName, response);
        return response;
    }

    private void checkAndBuildNewName(Integer categoryId, String serviceName,
        DataServiceDefaultValueResponse response) {
        int i = 0;
        while (checkServiceName(new CheckNameDataServiceRequest(null, serviceName, categoryId))) {
            i++;
            serviceName = response.getServiceName() + "_" + i;
        }
        response.setServiceName(serviceName);
    }

    @Override
    public List<DataServiceCategoryTreeResponse> searchFilterData(
        List<DataServiceCategoryTreeResponse> dataServiceCategoryTreeResponses, String search) {
        // 执行搜索
        if (search != null && !search.isEmpty()) {
            List<DataServiceCategoryTreeResponse> searchResults = new ArrayList<>();
            for (DataServiceCategoryTreeResponse root : dataServiceCategoryTreeResponses) {
                searchResults.addAll(root.searchTreeAsDataService(search));
            }
            dataServiceCategoryTreeResponses = searchResults;
        }
        return dataServiceCategoryTreeResponses;
    }

    @Override
    public TreeAddResponse cloneDataService(DataServiceCloneRequest request) {
        Integer categoryId = request.getCategoryId();
        DataServiceCategory dataServiceCategory = dataServiceCategoryMapper.selectById(categoryId);
        if (Objects.isNull(dataServiceCategory)) {
            throw new BizException(String.format("未知业务分类[categoryId:%s]", categoryId));
        }
        Integer sourceServiceId = request.getSourceServiceId();
        DataService dataService = dataServiceMapper.selectById(sourceServiceId);
        if (Objects.isNull(dataService)) {
            throw new BizException(String.format("未知业务分类[sourceServiceId:%s]", sourceServiceId));
        }
        dataService.setId(null);
        dataService.setCategoryId(categoryId);
        dataService.setAbilityCenterId(null);
        dataService.setCode(getDataServiceCode());
        dataService.setCreateTime(LocalDateTime.now());
        dataService.setUpdateTime(LocalDateTime.now());
        dataService.setPublishStatus(ServicePublishStatus.UNRELEASED);
        dataService.setHealthStatus(ServiceHealthStatus.UNCHECKED);
        dataService.setName(buildCopyName(dataService.getName(),
            name -> Objects.isNull(dataServiceMapper.selectByName(name))));
        DataServiceConfig dataServiceConfig = dataServiceConfigMapper.selectOneByDataServiceId(sourceServiceId);
        if (Objects.isNull(dataServiceConfig)) {
            throw new BizException(String.format("未知数据服务配置[sourceServiceId:%s]", sourceServiceId));
        }
        dataServiceMapper.insert(dataService);
        dataServiceConfig.setId(null);
        dataServiceConfig.setDataServiceId(dataService.getId());
        dataServiceConfigMapper.insert(dataServiceConfig);
        return new TreeAddResponse(ServiceCategoryTreeNode.SERVICE, dataService.getId());
    }

    /**
     * 构造复制名称； 如果名称不存在，就返回原始名称；否则返回：原始名称 + _copy + copy流水值
     *
     * @param name                      原始名称
     * @param checkNameNotExistFunction 检查名称不存在的函数
     * @return copyName
     */
    public static String buildCopyName(String name, Function<String, Boolean> checkNameNotExistFunction) {
        String copyName = name;
        for (int i = 1; ; i++) {
            if (checkNameNotExistFunction.apply(copyName)) {
                break;
            }
            if (copyName.matches("\\S*copy\\d*")) {
                int yLastIndex = copyName.lastIndexOf('y');
                copyName = copyName.substring(0, yLastIndex + 1) + i;
            } else {
                copyName = copyName + "_copy" + i;
            }
        }
        return copyName;
    }
}
