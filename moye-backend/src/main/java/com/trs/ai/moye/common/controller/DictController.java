package com.trs.ai.moye.common.controller;

import com.trs.ai.moye.common.enums.EnumDict;
import com.trs.ai.moye.common.response.DictResponse;
import com.trs.ai.moye.data.standard.response.DataSourceTypeDictResponse;
import com.trs.ai.moye.knowledgebase.service.KnowledgeBaseService;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.connection.enums.DataSourceCategory;
import com.trs.moye.base.data.connection.enums.FileExtension;
import com.trs.moye.base.knowledgebase.enums.KnowledgeBaseType;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 针对枚举字段的固定返回--字典查询
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/9/12 14:24
 **/
@RestController
@RequestMapping("/common/dict")
public class DictController {

    @Resource
    private KnowledgeBaseService knowledgeBaseService;

    /**
     * 获取数据源类型下拉框选项 <a href="http://192.168.210.40:3001/project/5419/interface/api/163570">...</a>
     *
     * @return 数据源类型下拉框选项
     * <AUTHOR>
     */
    @GetMapping("/connect-types/data-source")
    public List<DataSourceTypeDictResponse> getDataSourceTypes() {
        return Arrays.stream(DataSourceCategory.values()).filter(DataSourceCategory::isSupportSource)
            .map(DataSourceTypeDictResponse::new).toList();
    }

    /**
     * 获取数据源类型下拉框选项 <a href="http://192.168.210.40:3001/project/5419/interface/api/164480">...</a>
     *
     * @param isNeedGraphics 是否需要图形化
     * @return 数据源类型下拉框选项
     * <AUTHOR>
     */
    @GetMapping("/connect-types/data-storage")
    public List<DataSourceTypeDictResponse> getDataStorageTypes(
        @RequestParam(required = false) Boolean isNeedGraphics) {
        Predicate<ConnectionType> notNeedGraphicsFilter = connectionType -> connectionType != ConnectionType.NEBULA;
        return Arrays.stream(DataSourceCategory.values()).filter(DataSourceCategory::isSupportStorage)
            .map(category -> {
                DataSourceTypeDictResponse response = new DataSourceTypeDictResponse();
                response.setValue(category.name());
                response.setLabel(category.getLabel());
                Predicate<ConnectionType> filter = connectionType -> connectionType.getCategory() == category
                    && connectionType.isSupportStorage();
                if (isNeedGraphics != null && !isNeedGraphics) {
                    filter = filter.and(notNeedGraphicsFilter);
                }
                response.setSubTypes(ConnectionType.filterConnectionType(filter).stream()
                    .map(connectionType -> new DictResponse(connectionType.name(), connectionType.getLabel()))
                    .toList());
                return response;
            }).toList();
    }

    /**
     * 获取所有支持的文件后缀 <a href="">...</a>
     *
     * @return {@link FileExtension}列表
     */
    @GetMapping("/file/extensions")
    public List<FileExtension> getAllFileExtensions() {
        return List.of(FileExtension.values());
    }

    /**
     * 字段类型 <a href="http://192.168.210.40:3001/project/5419/interface/api/163765">...</a>
     *
     * @param includeObjectType 包含对象类型
     * @return 字段类型列表
     */
    @GetMapping("/field-type")
    public List<DictResponse> getFieldTypes(@RequestParam(defaultValue = "false") boolean includeObjectType) {
        List<DictResponse> enumResponseList = Arrays.stream(FieldType.values())
            .filter(fieldType -> !fieldType.isMoyeObjectType())
            .map(fieldType -> new DictResponse(fieldType.name(), fieldType.getLabel()))
            .collect(Collectors.toCollection(ArrayList::new));
        if (includeObjectType) {
            enumResponseList.addAll(getMoyeObjectTypes());
        }
        return enumResponseList;
    }

    private List<DictResponse> getMoyeObjectTypes() {
        return knowledgeBaseService.getKnowledgeBaseDictList(List.of(KnowledgeBaseType.values()));
    }

    /**
     * 知识库字段类型 <a href="http://192.168.210.40:3001/project/5419/interface/api/163765">...</a>
     *
     * @return 字段类型列表
     */
    @GetMapping("/field-type/knowledge-base")
    public List<DictResponse> getKnowledgeBaseFieldTypes() {
        return Arrays.stream(FieldType.values()).filter(FieldType::isSupportKnowledgeBase)
            .map(fieldType -> new DictResponse(fieldType.name(), fieldType.getLabel())).toList();
    }

    /**
     * 获取枚举字典列表 <a href="http://192.168.210.40:3001/project/5419/interface/api/164540">...</a>
     *
     * @param enumType 枚举类型
     * @return 字段类型列表
     */
    @GetMapping("/enum")
    public List<DictResponse> getEnumDictList(@RequestParam String enumType) {
        return EnumDict.valueOf(enumType).dictList();
    }

    /**
     * 根据DataSourceCategory类型获取ConnectionType列表
     *
     * @param category 数据源分类
     * @return ConnectionType列表
     */
    @GetMapping("/connect-types/by-category")
    public List<DictResponse> getConnectionTypesByCategory(@RequestParam DataSourceCategory category) {
        return ConnectionType.filterConnectionType(connectionType -> connectionType.getCategory() == category)
            .stream()
            .map(connectionType -> new DictResponse(connectionType.name(), connectionType.getLabel()))
            .toList();
    }
}
