package com.trs.ai.moye.monitor.entity;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.moye.base.common.response.PageResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 批处理监控列表统一返回
 *
 * <AUTHOR>
 * @since 2025/8/13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BatchTaskListAllResponse {

    /**
     * 分页数据
     */
    PageResponse<BatchTaskListResponse> pageResponse =  PageResponse.of(new Page<>());


    /**
     * 执行总数
     */
    private Long executeTotalCount = 0L;

    /**
     * 执行成功数
     */
    private Long executeSuccessCount = 0L;

    /**
     * 执行失败数量
     */
    private Long executeFailCount = 0L;

}
