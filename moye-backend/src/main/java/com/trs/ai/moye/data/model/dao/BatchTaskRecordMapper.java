package com.trs.ai.moye.data.model.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.ai.moye.common.dao.GroupCount;
import com.trs.ai.moye.data.model.entity.BatchTaskRecord;
import com.trs.ai.moye.data.model.entity.SparkEngineTracerData;
import com.trs.ai.moye.data.model.enums.BatchTaskStatus;
import com.trs.ai.moye.homepage.entity.HomePageDwdStatistics;
import com.trs.ai.moye.homepage.entity.HomePageSubjectStatistics;
import com.trs.ai.moye.homepage.entity.HomePageThemeStatistics;
import com.trs.ai.moye.monitor.entity.BatchTaskListResponse;
import com.trs.moye.base.common.request.SearchParams;
import com.trs.moye.base.common.request.SortParams;
import com.trs.moye.base.common.request.TimeRangeParams;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * BatchTaskRecord数据库操作类
 */
@DS("clickhouse")
@Mapper
public interface BatchTaskRecordMapper {


    /**
     * 分页查询数据处理监控列表
     *
     * @param timeRangeParams 时间范围
     * @param taskStatus      任务状态
     * @param modelId         模型id
     * @param searchParams    搜索参数
     * @param page            分页参数
     * @return {@link BatchTaskRecord}
     * <AUTHOR>
     * @since 2024/10/17 14:57
     */
    Page<BatchTaskRecord> selectBatchTaskMonitorList(@Param("timeRangeParams") TimeRangeParams timeRangeParams,
        @Param("taskStatus") BatchTaskStatus taskStatus, @Param("modelId") Integer modelId,
        @Param("searchParams") SearchParams searchParams, Page<SparkEngineTracerData> page);


    /**
     * 查询处理错误的数据数量
     *
     * @param id 数据建模ID
     * @return {@link Integer}
     * <AUTHOR>
     * @since 2024/10/21 15:56
     */
    Integer countErrorByDataModelId(@Param("id") Integer id);

    /**
     * 查询所有数据建模的未读异常数量
     *
     * @return {@link List}
     */
    List<GroupCount<Integer>> countAllUnreadErrors();

    /**
     * 更新已读信息状态
     *
     * @param id 数据id
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/10/21 17:52
     */
    Boolean updateReadErrorMessages(@Param("id") String id);

    /**
     * 查询最近一天的统计数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return {@link List}<{@link HomePageDwdStatistics}>
     */
    List<HomePageDwdStatistics> selectLastDayCountByModelId(@Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 查询批处理存储数量
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 返回
     */
    List<HomePageDwdStatistics> selectBatchStorage(@Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 根据建模查询存储量
     *
     * @param dataModelId 建模id
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param storageId   存储id
     * @return 存储量
     */
    Long getThemeStorageCount(@Param("dataModelId") Integer dataModelId, @Param("startTime") String startTime,
        @Param("endTime") String endTime, @Param("storageId") Integer storageId);

    /**
     * 根据建模查询执行量
     *
     * @param dataModelId 建模id
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 存储量
     */
    Long getThemeProcessCount(@Param("dataModelId") Integer dataModelId, @Param("startTime") String startTime,
        @Param("endTime") String endTime);

    /**
     * 查询所有批处理任务记录
     *
     * @return 批处理任务记录列表
     */
    List<BatchTaskRecord> selectAll();

    /**
     * 更新批处理任务记录
     *
     * @param writeCountInfoNulls 批处理任务记录
     */
    void updateWriteCountInfo(@Param("data") BatchTaskRecord writeCountInfoNulls);

    /**
     * 查询最近一天的统计数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return {@link List}<{@link HomePageDwdStatistics}>
     */
    List<HomePageThemeStatistics> selectLastDayCountByModelIdTheme(@Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 查询最近一天的统计数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return {@link List}<{@link HomePageDwdStatistics}>
     */
    List<HomePageSubjectStatistics> selectLastDayCountByModelIdSubject(@Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime);


    /**
     * 查询批处理存储数量
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 返回
     */
    List<HomePageThemeStatistics> selectBatchStorageTheme(@Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 查询批处理存储数量
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 返回
     */
    List<HomePageSubjectStatistics> selectBatchStorageSubject(@Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 根据执行id查询任务记录
     *
     * @param executeId 执行id
     * @return 任务记录
     */
    BatchTaskRecord selectByExecuteId(@Param("executeId") String executeId);

    /**
     * 获取批处理任务列表响应
     *
     * @param searchable 查询
     * @param sortable   排序
     * @param page       分页
     * @param status     状态
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return 批处理任务列表响应
     */
    Page<BatchTaskListResponse> getBatchTaskListResponse(@Param("searchable") SearchParams searchable,
        @Param("sortable") SortParams sortable, Page<Object> page, @Param("status") String status,
        @Param("startTime") String startTime, @Param("endTime") String endTime);
}