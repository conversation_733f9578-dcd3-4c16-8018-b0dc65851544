package com.trs.ai.moye.data.service.controller;

import com.trs.ai.moye.backstage.response.ApiLogTraceResponse;
import com.trs.ai.moye.common.response.TreeAddResponse;
import com.trs.ai.moye.common.utils.BeanUtil;
import com.trs.ai.moye.data.connection.response.DataStorageTreeResponse;
import com.trs.ai.moye.data.model.request.MoveRequest;
import com.trs.ai.moye.data.model.response.BatchDeleteResponse;
import com.trs.ai.moye.data.service.dao.DataServiceCategoryMapper;
import com.trs.ai.moye.data.service.dao.DataServiceMapper;
import com.trs.ai.moye.data.service.entity.DataService;
import com.trs.ai.moye.data.service.entity.DataServiceCategory;
import com.trs.ai.moye.data.service.enums.ServiceCategoryTreeNode;
import com.trs.ai.moye.data.service.enums.ServicePublishStatus;
import com.trs.ai.moye.data.service.request.CheckNameDataServiceRequest;
import com.trs.ai.moye.data.service.request.CodeBlockRequest;
import com.trs.ai.moye.data.service.request.DataServiceCategoryRequest;
import com.trs.ai.moye.data.service.request.DataServiceCloneRequest;
import com.trs.ai.moye.data.service.request.DataServiceLogRequest;
import com.trs.ai.moye.data.service.request.DataServicePreviewRequest;
import com.trs.ai.moye.data.service.request.DataServiceRequest;
import com.trs.ai.moye.data.service.response.DataServiceCategoryResponse;
import com.trs.ai.moye.data.service.response.DataServiceCategoryTreeResponse;
import com.trs.ai.moye.data.service.response.DataServiceDefaultValueResponse;
import com.trs.ai.moye.data.service.response.DataServiceLogResponse;
import com.trs.ai.moye.data.service.response.DataServiceResponse;
import com.trs.ai.moye.data.service.response.ability.AbilityCheckUnpublishResponse;
import com.trs.ai.moye.data.service.service.DataServiceLogService;
import com.trs.ai.moye.data.service.service.DataServiceService;
import com.trs.ai.moye.permission.service.DynamicUserNameService;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.response.IdNameResponse;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.utils.AssertUtils;
import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据服务controller
 *
 * <AUTHOR>
 * @since 2024/09/24 16:23:53
 */
@Validated
@RestController
@RequestMapping("/data-service")
public class DataServiceController {

    @Resource
    private DataServiceCategoryMapper dataServiceCategoryMapper;
    @Resource
    private DataServiceService dataServiceService;
    @Resource
    private DataStorageMapper dataStorageMapper;
    @Resource
    private DataServiceMapper dataServiceMapper;
    @Resource
    private DataConnectionMapper dataConnectionMapper;
    @Resource
    private DataServiceLogService dataServiceLogService;

    /**
     * 获取分类树
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/163770">【服务】目录树</a>
     *
     * @param search 搜索
     * @return {@link List }<{@link DataServiceCategoryTreeResponse }>
     * <AUTHOR>
     * @since 2024/09/24 16:23:54
     */
    @GetMapping("/category/tree")
    public List<DataServiceCategoryTreeResponse> getCategoryTree(@RequestParam(required = false) String search) {
        DynamicUserNameService dynamicUserNameService = BeanUtil.getBean(DynamicUserNameService.class);
        List<DataServiceCategoryTreeResponse> dataServiceCategoryTreeResponses = dataServiceService.getCategoryTree()
            .stream()
            .map(e -> new DataServiceCategoryTreeResponse(e, dynamicUserNameService)).toList();
        return dataServiceService.searchFilterData(dataServiceCategoryTreeResponses, search);
    }

    /**
     * 检查分类名称
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/163800">【服务分类】检查名称重复</a>
     *
     * @param request 请求
     * @return boolean
     * <AUTHOR>
     * @since 2024/09/24 16:23:54
     */
    @PostMapping("/category/check-name")
    public boolean checkCategoryName(@RequestBody @Validated DataServiceCategoryRequest request) {
        return dataServiceCategoryMapper.checkCategoryName(new DataServiceCategory(request));
    }

    /**
     * 添加分类
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/163810">【服务分类】新增</a>
     *
     * @param category 分类
     * @return {@link TreeAddResponse }
     * <AUTHOR>
     * @since 2024/09/24 16:23:56
     */
    @PostMapping("/category")
    public TreeAddResponse addCategory(@Validated @RequestBody DataServiceCategoryRequest category) {
        DataServiceCategory dataServiceCategory = new DataServiceCategory(category);
        dataServiceCategoryMapper.insert(dataServiceCategory);
        return new TreeAddResponse(ServiceCategoryTreeNode.SERVICE_CATEGORY, dataServiceCategory.getId());
    }

    /**
     * 获取分类详情
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/163785">【服务分类】详情</a>
     *
     * @param id id
     * @return {@link DataServiceCategoryResponse }
     * <AUTHOR>
     * @since 2024/09/24 17:25:16
     */
    @GetMapping("/category/{id}")
    public DataServiceCategoryResponse getCategory(@PathVariable @NotNull Integer id) {
        DynamicUserNameService dynamicUserNameService = BeanUtil.getBean(DynamicUserNameService.class);
        DataServiceCategory dataServiceCategory = dataServiceCategoryMapper.selectById(id);
        checkServiceCategory(id);
        return new DataServiceCategoryResponse(dataServiceCategory, dynamicUserNameService);
    }

    /**
     * 更新分类
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/163820">【服务分类】编辑</a>
     *
     * @param id       id
     * @param category 分类
     * <AUTHOR>
     * @since 2024/09/24 16:23:57
     */
    @PutMapping("/category/{id}")
    public void updateCategory(@PathVariable @NotNull Integer id, @RequestBody DataServiceCategoryRequest category) {
        category.setId(id);
        checkServiceCategory(id);
        dataServiceCategoryMapper.updateById(new DataServiceCategory(category));
    }

    private void checkServiceCategory(Integer id) {
        AssertUtils.notEmpty(dataServiceCategoryMapper.selectById(id),
            String.format("数据库不存在主键为【%s】的数据服务分类", id));
    }

    /**
     * 删除分类
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/163825">【服务分类】删除</a>
     *
     * @param id id
     * <AUTHOR>
     * @since 2024/09/24 16:23:58
     */
    @DeleteMapping("/category/{id}")
    public void deleteCategory(@PathVariable @NotNull Integer id) {
        DataServiceCategory dataServiceCategory = dataServiceCategoryMapper.selectById(id);
        AssertUtils.empty(dataServiceCategoryMapper.selectCategoryTreeByPid(id),
            String.format("【%s】分类下存在节点,请检查后重试!", dataServiceCategory.getName()));
        AssertUtils.empty(dataServiceMapper.selectByCategoryId(id),
            String.format("【%s】分类下存在服务,请检查后重试!", dataServiceCategory.getName()));
        dataServiceCategoryMapper.deleteById(id);
    }

    /**
     * 添加数据服务
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/163925">【服务】新增</a>
     *
     * @param request 请求
     * @return {@link TreeAddResponse }
     * <AUTHOR>
     * @since 2024/09/25 17:36:15
     */
    @PostMapping
    public TreeAddResponse addDataService(@Validated @RequestBody DataServiceRequest request) {
        checkServiceRequest(request);
        return dataServiceService.addDataService(request);
    }

    /**
     * 克隆数据服务
     *
     * @param request 克隆请求
     * @return {@link TreeAddResponse }
     */
    @PostMapping("/clone")
    public TreeAddResponse cloneDataService(@Validated @RequestBody DataServiceCloneRequest request) {
        return dataServiceService.cloneDataService(request);
    }

    /**
     * 更新数据服务
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/163950@param">【服务】修改</a>
     *
     * @param request 请求
     * <AUTHOR>
     * @since 2024/09/25 17:36:15
     */
    @PutMapping
    public void updateDataService(@Validated @RequestBody DataServiceRequest request) {
        checkServiceRequest(request);
        dataServiceService.updateDataService(request);
    }

    /**
     * 获取详细信息
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/163860">【数据服务-详情】基本信息</a>
     *
     * @param id id
     * @return {@link DataServiceResponse }
     * <AUTHOR>
     * @since 2024/09/27 18:08:41
     */
    @GetMapping("/{id}")
    public DataServiceResponse getDetail(@PathVariable @NotNull Integer id) {
        return dataServiceService.getDetail(id);
    }

    /**
     * 删除数据服务
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/163930">【服务】删除</a>
     *
     * @param ids id
     * @return BatchDeleteResponse
     * <AUTHOR>
     * @since 2024/09/29 11:26:01
     */
    @DeleteMapping()
    public List<BatchDeleteResponse> deleteDataService(@RequestBody @NotEmpty List<Integer> ids) {
        List<BatchDeleteResponse> batchDeleteResultList = new ArrayList<>();
        if (Objects.isNull(ids) || ids.isEmpty()) {
            return batchDeleteResultList;
        }
        //查询所有的服务
        List<DataService> dataServiceList = dataServiceMapper.selectByIds(ids);
        //检查服务是否已发布

        List<DataService> deleteServiceList = new ArrayList<>();
        for (DataService dataService : dataServiceList) {
            if (ServicePublishStatus.RELEASED.equals(dataService.getPublishStatus())) {
                BatchDeleteResponse batchDeleteResponse = new BatchDeleteResponse(dataService,
                    "该服务已经被发布，无法进行删除操作！", Boolean.FALSE);
                batchDeleteResultList.add(batchDeleteResponse);
            } else {
                deleteServiceList.add(dataService);
            }
        }

        List<Integer> deleteIds = deleteServiceList.stream().map(DataService::getId).toList();

        if (!deleteIds.isEmpty()) {
            try {
                //执行删除
                dataServiceService.deleteDataService(deleteIds);
                List<BatchDeleteResponse> batchDeleteResponses = deleteServiceList.stream()
                    .map(dataService -> new BatchDeleteResponse(dataService,
                        "", Boolean.TRUE)).toList();
                batchDeleteResultList.addAll(batchDeleteResponses);
            } catch (Exception e) {
                List<BatchDeleteResponse> batchDeleteResponses = deleteServiceList.stream()
                    .map(dataService -> new BatchDeleteResponse(dataService,
                        "删除失败!详细原因：" + e.getMessage(), Boolean.FALSE)).toList();
                batchDeleteResultList.addAll(batchDeleteResponses);
            }
        }

        return batchDeleteResultList;
    }


    /**
     * 批量移动 <a href="http://192.168.210.40:3001/project/5419/interface/api/165985">...</a>
     *
     * @param request 前端请求
     * @return {@link boolean}
     * <AUTHOR>
     * @since 2024/12/4 14:40
     */
    @PostMapping("/batch-move")
    public boolean batchMove(@RequestBody @Valid MoveRequest request) {
        if (Objects.isNull(request.getIds()) || request.getIds().isEmpty()) {
            return Boolean.FALSE;
        }
        return dataServiceService.moveMetaDataToCategory(request.getIds(), request.getBusinessId());

    }

    /**
     * 发布数据服务
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/163970">【服务】发布</a>
     *
     * @param id id
     * @return boolean
     * <AUTHOR>
     * @since 2024/10/09 14:31:48
     */
    @PostMapping("/{id}/publish")
    public boolean publishDataService(@PathVariable @NotNull Integer id) {
        checkService(id);
        return dataServiceService.publish(id);
    }

    /**
     * 检查服务名称
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/163900">【服务】检查名称重复</a>
     *
     * @param request 请求
     * @return boolean
     * <AUTHOR>
     * @since 2024/10/09 14:42:50
     */
    @PostMapping("/check-name")
    public boolean checkServiceName(@RequestBody @Validated CheckNameDataServiceRequest request) {
        return dataServiceService.checkServiceName(request);
    }

    /**
     * 检查服务是否可以撤销发布
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/163985">【服务】检查撤销发布</a>
     *
     * @param id id
     * @return {@link AbilityCheckUnpublishResponse }
     * <AUTHOR>
     * @since 2024/10/09 17:08:51
     */
    @PostMapping("/{id}/check-unpublish")
    public AbilityCheckUnpublishResponse checkUnpublish(@PathVariable @NotNull Integer id) {
        checkService(id);
        return dataServiceService.checkUnpublish(id);
    }

    /**
     * 撤销发布
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/163980">【服务】检查撤销发布</a>
     *
     * @param id id
     * @return boolean
     * <AUTHOR>
     * @since 2024/10/09 17:12:52
     */
    @PostMapping("/{id}/unpublish")
    public boolean unpublish(@PathVariable @NotNull Integer id) {
        checkService(id);
        return dataServiceService.unpublish(id);
    }

    /**
     * 重新发布
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/164715">【服务】重新发布</a>
     *
     * @param id id
     * @return boolean
     * <AUTHOR>
     * @since 2024/10/09 18:47:13
     */
    @PostMapping("/{id}/republish")
    public boolean republish(@PathVariable @NotNull Integer id) {
        checkService(id);
        return dataServiceService.republish(id);
    }

    /**
     * 获取发布状态
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/164750">【服务】获取发布状态</a>
     *
     * @param id id
     * @return {@link ServicePublishStatus }
     * <AUTHOR>
     * @since 2024/10/10 14:27:30
     */
    @GetMapping("/{id}/publish-status")
    public ServicePublishStatus getPublishStatus(@PathVariable @NotNull Integer id) {
        return dataServiceService.getPublishStatus(id);
    }

    /**
     * 获取可引用条件列表
     *
     * @param storageId 存储id
     * @param serviceId serviceId
     * @return {@link List }<{@link IdNameResponse }>
     * <AUTHOR>
     * @since 2024/10/12 17:58:17
     */
    @GetMapping("/ref-condition/list")
    public List<IdNameResponse> getRefConditionList(
        @RequestParam @NotNull(message = "存储id不能为空") Integer storageId,
        @RequestParam(required = false) Integer serviceId) {
        return dataServiceService.getRefConditionList(storageId, serviceId);
    }

    /**
     * 代码编辑提示，包含示例代码
     *
     * @param dbType DB 类型
     * @return {@link String }
     * @throws BizException 业务异常类
     * <AUTHOR>
     * @since 2024/10/14 10:28:49
     */
    @GetMapping("/{dbType}/tips")
    public String getTips(@PathVariable("dbType") ConnectionType dbType) throws BizException {
        return dataServiceService.getTips(dbType);
    }

    /**
     * 代码解析
     *
     * @param request 请求
     * @return {@link List }<{@link String }>
     * @throws BizException 业务异常类
     * <AUTHOR>
     * @since 2024/10/14 10:32:05
     */
    @PostMapping("/code-analysis")
    public List<String> analyseCode(@Validated @RequestBody CodeBlockRequest request) throws BizException {
        return dataServiceService.analyseCode(request);
    }

    /**
     * 检查服务请求
     *
     * @param request 请求
     * <AUTHOR>
     * @since 2024/09/25 17:36:28
     */
    private void checkServiceRequest(DataServiceRequest request) {
        // 检查分类是否存在
        checkServiceCategory(request.getCategoryId());
        if (Objects.nonNull(request.getConnectionId()) && Objects.isNull(
            dataConnectionMapper.selectById(request.getConnectionId()))) {
            throw new BizException(String.format("数据库不存在主键为【%s】的连接信息", request.getConnectionId()));
        }
        // 检查存储是否存在
        if (Objects.nonNull(request.getStorageId()) && Objects.isNull(
            dataStorageMapper.selectById(request.getStorageId()))) {
            throw new BizException(String.format("数据库不存在主键为【%s】的存储信息", request.getStorageId()));
        }
        // 检查服务名称是否重复
        if (Boolean.TRUE.equals(dataServiceService.checkServiceName(new CheckNameDataServiceRequest(request)))) {
            throw new BizException("服务名称重复");
        }
    }

    private void checkService(Integer id) {
        AssertUtils.notEmpty(dataServiceMapper.selectById(id), String.format("数据库不存在主键为【%s】的数据服务", id));
    }

    /**
     * 数据服务预览接口
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/163910">【数据服务】预览</a>
     *
     * @param request 请求参数
     * @return {@link PageResponse}
     */
    @PostMapping("/preview")
    public PageResponse<Map<String, Object>> preview(@Validated @RequestBody DataServicePreviewRequest request) {
        return dataServiceService.preview(request);
    }

    /**
     * 获取存储树
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/164885">...</a>
     *
     * @return {@link List }<{@link DataStorageTreeResponse }>
     * <AUTHOR>
     * @since 2024/10/12 15:57:47
     */
    @GetMapping("/code-mode/storage-tree")
    public List<DataStorageTreeResponse> getStorageTree() {
        return dataServiceService.getStorageTree();
    }

    /**
     * 导出数据预览 CSV
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/165435">导出全部预览数据</a>
     *
     * @param request  请求
     * @param response 响应
     * @throws BizException 业务异常类
     * @throws IOException  io异常
     * <AUTHOR>
     * @since 2024/11/07 17:16:48
     */
    @PostMapping("/preview/export")
    public void exportDataPreviewCsv(@Validated @RequestBody DataServicePreviewRequest request,
        HttpServletResponse response) throws BizException, IOException {
        dataServiceService.exportDataPreviewCsv(request, response);
    }

    /**
     * 获取默认值
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/167785">获取默认值</a>
     *
     * @param categoryId  分类id
     * @param dataModelId 数据模型id
     * @return {@link DataServiceDefaultValueResponse}
     */
    @GetMapping("/default-value/{categoryId}/{dataModelId}")
    public DataServiceDefaultValueResponse getDefaultValue(@PathVariable Integer categoryId,
        @PathVariable Integer dataModelId) {
        return dataServiceService.getDefaultValue(categoryId, dataModelId);
    }

    /**
     * 获取数据服务日志列表
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/167785">获取默认值</a>
     *
     * @param request 请求参数
     * @return {@link PageResponse }<{@link DataServiceLogResponse }>
     */
    @PostMapping("/log/page-list")
    public PageResponse<DataServiceLogResponse> getLogPageList(@RequestBody DataServiceLogRequest request) {
        return dataServiceLogService.getPageList(request).toNewPageResult(DataServiceLogResponse::of);
    }

    /**
     * 获取数据服务日志执行链路
     *
     * @param logId 日志记录id
     * @return {@link List }<{@link ApiLogTraceResponse }>
     */
    @GetMapping("/log/{logId}/tracer")
    public List<ApiLogTraceResponse> getTracerList(@PathVariable Long logId) {
        return dataServiceLogService.getTracerList(logId);
    }

}