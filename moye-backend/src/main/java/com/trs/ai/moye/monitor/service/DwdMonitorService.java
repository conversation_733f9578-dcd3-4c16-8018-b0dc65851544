package com.trs.ai.moye.monitor.service;

import com.trs.ai.moye.monitor.entity.BatchTaskListAllResponse;
import com.trs.ai.moye.monitor.entity.BatchTaskListRequest;
import com.trs.ai.moye.monitor.request.HomePageDwdTrendRequest;
import com.trs.ai.moye.monitor.response.statistics.BatchTaskExecuteResponse;
import com.trs.ai.moye.monitor.response.statistics.StreamTaskBarChart;
import com.trs.ai.moye.monitor.response.statistics.StreamTaskHandleResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/8
 */
public interface DwdMonitorService {

    /**
     * 获取批量任务执行趋势
     *
     * @param request 请求参数
     * @return 批处理任务执行趋势
     */
    BatchTaskExecuteResponse getBatchTaskExecuteResponse(HomePageDwdTrendRequest request);

    /**
     * 获取流处理任务处理趋势
     *
     * @param request 请求参数
     * @return 流处理任务处理趋势
     */
    StreamTaskHandleResponse getStreamTaskHandleResponse(HomePageDwdTrendRequest request);

    /**
     * 获取流处理任务柱状图
     *
     * @param request 请求参数
     * @return 流处理任务柱状图
     */
    List<StreamTaskBarChart> getStreamTaskBarChart(HomePageDwdTrendRequest request);

    /**
     * 获取存储的折线图
     *
     * @param request 请求参数
     * @return 存储的折线图
     */
    StreamTaskHandleResponse getStorageLine(HomePageDwdTrendRequest request);

    /**
     * 获取算子执行总数
     *
     * @return 算子执行总数
     */
    Long operatorExecuteCount();

    /**
     * 获取批处理任务列表
     *
     * @param request 请求参数
     * @return 批处理任务列表
     */
    BatchTaskListAllResponse getBatchTaskListResponse(BatchTaskListRequest request);
}
