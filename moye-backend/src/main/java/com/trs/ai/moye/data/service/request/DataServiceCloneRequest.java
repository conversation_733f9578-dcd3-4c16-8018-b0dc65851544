package com.trs.ai.moye.data.service.request;

import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据服务克隆请求
 *
 * <AUTHOR>
 * @since 2025/8/14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataServiceCloneRequest {

    /**
     * 克隆数据服务的id
     */
    @NotNull(message = "克隆数据服务的id不能为空")
    private Integer sourceServiceId;

    /**
     * 克隆后的数据服务分类id
     */
    @NotNull(message = "克隆后的数据服务分类id不能为空")
    private Integer categoryId;

}
