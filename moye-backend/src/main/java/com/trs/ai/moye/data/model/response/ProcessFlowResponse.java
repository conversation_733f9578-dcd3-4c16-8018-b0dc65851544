package com.trs.ai.moye.data.model.response;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.ai.moye.data.model.entity.StorageTaskTrace;
import com.trs.moye.ability.domain.DataProcessRecord;
import com.trs.moye.ability.domain.DataProcessTrace;
import com.trs.moye.base.common.enums.TaskStatus;
import com.trs.moye.base.common.log.enums.DataTracerTypeEnum;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.service.entity.ValueObject;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 算子处理链路响应实体
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/5/16 18:32
 **/
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class ProcessFlowResponse {

    /**
     * 数据id
     */
    private String recordId;
    /**
     * 执行id
     */
    private String executeId;
    /**
     * 算子处理id
     */
    private String processId;
    /**
     * 步骤标题
     */
    private String title;
    /**
     * 是否异常
     */
    private TaskStatus isException;
    /**
     * 类型
     */
    private String type;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 处理时长
     */
    private Long duration;
    /**
     * 子流程列表
     */
    private List<ProcessFlowResponse> children;
    /**
     * 数据源名称
     */
    private String dataSourceName;
    /**
     * 异常信息
     */
    private String exception;
    /**
     * 编排算子id
     */
    private Integer operatorId;
    /**
     * 能力id
     */
    private Integer abilityId;
    /**
     * 能力名称
     */
    private String abilityName;
    /**
     * 输入参数
     */
    private List<ValueObject> origin;
    /**
     * 输出参数
     */
    private List<ValueObject> result;
    /**
     * 是否重跑
     */
    private Integer isRerun;
    /**
     * 是否重复消费
     */
    private Integer isDuplicate = 0;


    /**
     * 构造ProcessFlowResponse
     *
     * @param storageTaskTrace 贴源表数据接入流程
     * <AUTHOR>
     * @since 2025/1/22 15:30
     */
    public ProcessFlowResponse(StorageTaskTrace storageTaskTrace) {
        this.processId = storageTaskTrace.getId();
        this.title = storageTaskTrace.getNode().getLabel();
        this.isException = isException(storageTaskTrace.getIsError());
        this.type = storageTaskTrace.getNode().name();
        this.startTime = storageTaskTrace.getStartTime();
        this.duration = storageTaskTrace.getProcessingTime();
        this.children = Collections.emptyList();
    }

    private TaskStatus isException(Integer isError) {
        return Objects.nonNull(isError) && isError == 1 ? TaskStatus.FAILED : TaskStatus.SUCCESS;
    }

    public ProcessFlowResponse(DataProcessRecord record) {
        this.recordId = Long.toString(record.getRecordId());
        this.executeId = Long.toString(record.getExecuteId());
        this.title = DataTracerTypeEnum.DATA_PROCESSING.getNodeName();
        this.startTime = record.getStartTime();
        this.duration = record.getProcessingTime();
        this.isRerun = record.getIsRerun();
        this.isException = isException(record.getIsError());
    }

    public ProcessFlowResponse(DataProcessTrace trace, String abilityName) {
        this.recordId = Long.toString(trace.getRecordId());
        this.executeId = Long.toString(trace.getExecuteId());
        this.processId = Long.toString(trace.getProcessId());
        this.title = trace.getProcessingName();
        this.startTime = trace.getStartTime();
        this.duration = trace.getProcessingTime();
        this.dataSourceName = trace.getDataModelName();
        this.isException = isException(trace.getIsError());
        this.exception = trace.getErrorMsg();
        this.operatorId = trace.getOperatorId();
        this.origin = toValues(trace.getInput());
        this.result = toValues(trace.getOutput());
        this.abilityId = trace.getAbilityId();
        this.abilityName = abilityName;
    }

    /**
     * 将JSON字符串转换为ValueObject列表
     *
     * @param json JSON字符串
     * @return ValueObject列表
     */
    public static List<ValueObject> toValues(String json) {
        if (json == null || json.isEmpty() || "null".equals(json) || "{}".equals(json)) {
            return Collections.emptyList();
        }
        List<ValueObject> valueObjects = new ArrayList<>();
        JsonNode jsonNode = JsonUtils.parseJsonNode(json);
        if (jsonNode.isObject()) {
            jsonNode.fields().forEachRemaining(entry ->
                    valueObjects.add(new ValueObject(entry.getKey(), JsonUtils.nodeToString(entry.getValue()))));
        } else {
            // 如果是数组或其他类型，直接转换为字符串
            valueObjects.add(new ValueObject("result", json));
        }

        return valueObjects;
    }
}
