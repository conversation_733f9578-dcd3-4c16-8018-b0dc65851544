package com.trs.ai.moye.data.model.entity;


import com.trs.moye.base.data.execute.ExecuteModeEnum;
import com.trs.moye.base.data.model.enums.CreateModeEnum;
import com.trs.moye.base.data.model.enums.ModelExecuteStatus;
import com.trs.moye.base.common.enums.ModelLayer;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 元数据和分层、业务分类关联信息
 *
 * <AUTHOR>
 * @since 2024/9/26 17:58
 */
@Data
public class MetadataRelation {

    /**
     * 建模
     */
    private Integer dataModelId;
    /**
     * 建模名称
     */
    private String dataModelName;
    /**
     * 建模英文名
     */
    private String dataModelEnName;
    /**
     * 数仓分层
     */
    private ModelLayer modelLayer;
    /**
     * 业务分类id
     */
    private Integer categoryId;

    /**
     * 调度类型 1-定时
     */
    private ExecuteModeEnum executeMode;

    /**
     * 最近更新时间
     */
    private LocalDateTime recentUpdateTime;

    /**
     * 启动状态
     */
    private ModelExecuteStatus status;

    /**
     * 建模类型
     */
    private CreateModeEnum dataModelType;
    /**
     * 连接ID
     */
    private Integer connectionId;
    /**
     * 数据源大类
     */
    private String dataSourceCategory;
    /**
     * 是否已发布到MCP
     */
    private Boolean isMcpPublished;

}
