package com.trs.ai.moye.common.validation.util;

import com.trs.ai.moye.data.service.entity.query.Condition;
import com.trs.ai.moye.data.service.enums.DataServiceConditionType;

/**
 * 条件类型检查器
 */
public class ConditionTypeChecker {

    /**
     *  判断条件是否为向量字段
     *
     * @param c 条件对象
     * @return boolean
     */
    public boolean isVectorField(Condition c) {
        if (c == null || c.getKey() == null || c.getKey().getType() == null) {
            return false;
        }
        return c.getKey().getType().isVectorType();
    }

    /**
     * 判断条件是否有效
     *
     * @param c 条件对象
     * @return boolean
     */
    public boolean isValidCondition(Condition c) {
        return c != null && c.getOperator() != null;
    }

    /**
     * 判断条件是否为逻辑条件
     *
     * @param c 条件对象
     * @return boolean
     */
    public boolean isLogic(Condition c) {
        return c != null && c.getType() == DataServiceConditionType.LOGIC;
    }

    /**
     * 判断条件是否为表达式
     *
     * @param c 条件对象
     * @return boolean
     */
    public boolean isExpression(Condition c) {
        return c != null && c.getType() != DataServiceConditionType.LOGIC;
    }

    /**
     * 判断条件是否为逻辑连接符（AND/OR）
     *
     * @param c 条件对象
     * @return boolean
     */
    public boolean isAndOr(Condition c) {
        if (c == null || c.getOperator() == null) {
            return false;
        }
        String op = c.getOperator().trim().toLowerCase();
        return "and".equals(op) || "or".equals(op);
    }

    /**
     *  判断条件是否为NOT逻辑
     *
     * @param c 条件对象
     * @return boolean
     */
    public boolean isNot(Condition c) {
        return c != null && c.getOperator() != null && "not".equalsIgnoreCase(c.getOperator().trim());
    }

    /**
     *  判断条件是否为左括号
     *
     * @param c 条件对象
     * @return boolean
     */
    public boolean isLeftBracket(Condition c) {
        return c != null && c.getOperator() != null && "(".equals(c.getOperator().trim());
    }

    /**
     * 判断条件是否为右括号
     *
     * @param c 条件对象
     * @return boolean
     */
    public boolean isRightBracket(Condition c) {
        return c != null && c.getOperator() != null && ")".equals(c.getOperator().trim());
    }

    /**
     * 判断逻辑操作符是否有效
     *
     * @param op 逻辑操作符
     * @return boolean
     */
    public boolean isAllowedLogicOperator(String op) {
        if (op == null) {
            return false;
        }
        String o = op.toLowerCase();
        return "and".equals(o) || "or".equals(o) || "not".equals(o) || "(".equals(o) || ")".equals(o);
    }

    /**
     * 判断两个条件是否可以逻辑组合
     *
     * @param prev 前一个条件
     * @param curr 当前条件
     * @return boolean
     */
    public boolean isValidLogicCombination(Condition prev, Condition curr) {
        return isLeftBracket(prev) && isLeftBracket(curr)
                ||
                isRightBracket(prev) && isRightBracket(curr)
                ||
                isAndOr(prev) && (isNot(curr) || isLeftBracket(curr))
                ||
                isNot(prev) && isLeftBracket(curr)
                ||
                isRightBracket(prev) && isAndOr(curr)
                ||
                isLeftBracket(prev) && isNot(curr);
    }

    /**
     * 判断最后一个元素是否无效
     *
     * @param last 最后一个条件
     * @return boolean
     */
    public boolean isInvalidLastElement(Condition last) {
        return isAndOr(last) || isNot(last) || isLeftBracket(last);
    }
}
