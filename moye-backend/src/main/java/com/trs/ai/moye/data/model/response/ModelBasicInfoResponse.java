package com.trs.ai.moye.data.model.response;

import com.trs.moye.base.data.model.enums.CreateModeEnum;
import com.trs.moye.base.common.response.IdNameResponse;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.enums.ModelExecuteStatus;
import com.trs.moye.base.common.enums.ModelLayer;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据建模基本信息响应
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-26 21:26
 */
@Data
@NoArgsConstructor
public class ModelBasicInfoResponse {

    /**
     * 主键
     */
    protected Integer id;

    /**
     * 中文名
     */
    private String zhName;

    /**
     * 英文名
     */
    private String enName;

    /**
     * 创建模式： 数据接入、逆向建模、视图
     */
    private CreateModeEnum createMode;

    /**
     * 执行状态
     */
    private ModelExecuteStatus executeStatus;

    /**
     * 业务分类
     */
    private IdNameResponse businessCategory;

    /**
     * 分层
     */
    private ModelLayer layer;

    /**
     * 说明
     */
    private String description;

    /**
     * 创建人
     */
    private IdNameResponse createUser;

    /**
     * 修改人
     */
    private IdNameResponse updateUser;

    /**
     * 创建时间
     */
    protected LocalDateTime createTime;

    /**
     * 更新时间
     */
    protected LocalDateTime updateTime;

    /**
     * 是否发布到数据智能体
     */
    private Boolean isMcpPublished;


    public ModelBasicInfoResponse(DataModel model) {
        this.id = model.getId();
        this.enName = model.getEnName();
        this.zhName = model.getZhName();
        this.createMode = model.getCreateMode();
        this.executeStatus = model.getExecuteStatus();
        this.layer = model.getLayer();
        this.description = model.getDescription();
        this.createTime = model.getCreateTime();
        this.updateTime = model.getUpdateTime();
        this.isMcpPublished = model.getIsMcpPublished();
    }
}
