package com.trs.ai.moye.monitor.entity;

import com.trs.moye.base.common.request.PageParams;
import com.trs.moye.base.common.request.SearchParams;
import com.trs.moye.base.common.request.SortParams;
import com.trs.moye.base.common.request.TimeRangeParams;
import javax.validation.Valid;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/8/7
 */
@Data
public class BatchTaskListRequest {

    /**
     * 检索参数
     */
    @Valid
    protected SearchParams searchParams;

    /**
     * 排序参数
     */
    @Valid
    protected SortParams sortParams;

    /**
     * 分页参数
     */
    private PageParams pageParams;

    /**
     * 上次状态
     */
    private String lastStatus;

    /**
     * 时间参数
     */
    private TimeRangeParams timeRangeParams;


}
