package com.trs.ai.moye.data.ability.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.ai.moye.common.entity.UsageInfoResponse;
import com.trs.ai.moye.common.entity.UsageInfoResponse.UsingObjects;
import com.trs.ai.moye.common.enums.NameTypeEnum;
import com.trs.ai.moye.common.response.TreeBaseResponse;
import com.trs.ai.moye.data.ability.dao.AbilityMapper;
import com.trs.ai.moye.data.ability.request.AbilityRequest;
import com.trs.ai.moye.data.ability.request.AbilityTestRequest;
import com.trs.ai.moye.data.ability.request.AbilityTreeRequest;
import com.trs.ai.moye.data.ability.request.SearchTypeArrayAbilityTreeRequest;
import com.trs.ai.moye.data.ability.response.AbilityAddResponse;
import com.trs.ai.moye.data.ability.response.AbilityDetailResponse;
import com.trs.ai.moye.data.ability.response.AbilityResponse;
import com.trs.ai.moye.data.ability.response.AbilityTestNewResponse;
import com.trs.ai.moye.data.model.dao.batch.BatchOperatorMapper;
import com.trs.ai.moye.data.model.dao.operator.OperatorNewMapper;
import com.trs.ai.moye.data.model.response.KeyValueResponse;
import com.trs.ai.moye.data.model.service.impl.CacheService;
import com.trs.ai.moye.data.operator.constants.enums.OperatorTestStatus;
import com.trs.ai.moye.data.operator.constants.enums.OperatorTreeNodeTypeEnum;
import com.trs.ai.moye.data.operator.domain.response.AbilityArrangementTreeResponse;
import com.trs.ai.moye.data.operator.domain.vo.AbilityDTO;
import com.trs.ai.moye.data.operator.service.OperatorAbilityCenterService;
import com.trs.ai.moye.data.operator.service.OperatorCategoryService;
import com.trs.ai.moye.streamengine.feign.StreamEngineFeign;
import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.entity.AbilityExecuteParams;
import com.trs.moye.ability.entity.Schema;
import com.trs.moye.ability.entity.operator.AbilityCenterCallParams;
import com.trs.moye.ability.enums.AbilityCategory;
import com.trs.moye.ability.enums.AbilityTestStatus;
import com.trs.moye.ability.enums.AbilityType;
import com.trs.moye.ability.enums.AbilityUpdateStatus;
import com.trs.moye.base.common.entity.ModuleEnum;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.response.ResponseMessage;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.model.dao.OperatorBusinessCategoryMapper;
import com.trs.moye.base.data.model.entity.OperatorBusinessCategory;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 能力服务实现
 *
 * <AUTHOR>
 * @since 2025/02/27 17:57:34
 */
@Slf4j
@Service
public class AbilityServiceImpl implements AbilityService {

    @Resource
    private AbilityMapper abilityMapper;

    @Resource
    private OperatorCategoryService operatorCategoryService;

    @Resource
    private OperatorBusinessCategoryMapper operatorCategoryMapper;

    @Resource
    private OperatorNewMapper operatorNewMapper;

    @Resource
    private BatchOperatorMapper batchOperatorMapper;

    @Resource
    private StreamEngineFeign streamEngineFeign;

    @Resource
    private CacheService cacheService;

    @Resource
    private OperatorAbilityCenterService operatorAbilityCenterService;


    @Override
    public AbilityResponse selectById(Integer id) {
        Ability ability = abilityMapper.selectById(id);
        OperatorBusinessCategory category = operatorCategoryMapper.selectById(ability.getOperatorCategoryId());
        if (category == null) {
            throw new BizException("id为%d的算子业务分类不存在！", ability.getOperatorCategoryId());
        }
        AbilityResponse abilityResponse = new AbilityResponse(ability);
        abilityResponse.setOperatorCategory(category.getZhName());
        return abilityResponse;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public boolean updateAbility(Integer id, AbilityRequest request) {
        Ability exist = abilityMapper.selectById(id);
        if (Objects.isNull(exist)) {
            throw new BizException("能力不存在");
        }
        // 获取使用了该算子的编排，更新redis中的算子编排信息
        UsageInfoResponse usage = getAbilityUsageInfo(id);
        List<KeyValueResponse> keyValueResponses = usage.getDetail().stream()
            .filter(objects -> objects.getType().equals(ModuleEnum.DATA_MODELING))
            .flatMap(usingObjects -> usingObjects.getObjects().stream())
            .toList();
        for (KeyValueResponse keyValueResponse : keyValueResponses) {
            cacheService.updateOperatorPipeline(id, keyValueResponse.getKey());
        }
        Ability ability = buildAbility(request);
        ability.setId(id);
        // 是能力中心算子并且version不同时，更新updateStatus
        if (exist.getType().equals(AbilityType.ABILITY_CENTER)
            && !exist.getAbilityCenterParams().getVersion().equals(request.getAbilityCenterParams().getVersion())) {
            ability.setUpdateStatus(AbilityUpdateStatus.UPDATED);
            ability.setTestStatus(AbilityTestStatus.UNTESTED);
        }
        return abilityMapper.updateById(ability) > 0;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public boolean deleteAbility(Integer id) {
        checkAbilityExist(id);
        UsageInfoResponse usage = getAbilityUsageInfo(id);
        if (usage.isUse()) {
            throw new BizException(usage.getMessage());
        }
        return abilityMapper.deleteById(id) > 0;
    }

    @Override
    @Transactional
    public AbilityAddResponse addAbility(AbilityRequest request) {
        if (abilityCheckName(NameTypeEnum.EN, request.getEnName())) {
            throw new BizException("英文名【%s】已存在！", request.getEnName());
        }
        if (abilityCheckName(NameTypeEnum.ZH, request.getZhName())) {
            throw new BizException("中文名【%s】已存在！", request.getZhName());
        }
        if (Objects.isNull(request.getOperatorCategoryId())) {
            throw new BizException("算子业务分类不能为空！");
        }
        Ability ability = buildAbility(request);
        abilityMapper.insert(ability);
        return new AbilityAddResponse(OperatorTreeNodeTypeEnum.OPERATOR, ability.getId());
    }

    @Override
    public List<TreeBaseResponse> abilityTree(AbilityTreeRequest request) {
        List<TreeBaseResponse> categoryTree = operatorCategoryService.operatorCategoryList();
        SearchTypeArrayAbilityTreeRequest searchRequest = new SearchTypeArrayAbilityTreeRequest(request);
        List<Ability> abilities = abilityMapper.selectAllByRequest(searchRequest);
        // 能力中心算子同步更新状态
        List<AbilityDTO> abilityDtos = operatorAbilityCenterService.getAllAbilityApiList();
        abilities.forEach(ability -> {
            if (ability.getType().equals(AbilityType.ABILITY_CENTER)) {
                Optional<AbilityDTO> optional = abilityDtos.stream()
                    .filter(dto -> dto.getPowerEnName().equals(ability.getEnName())).findFirst();
                if (optional.isPresent()) {
                    // 如果能力中心算子有版本信息，则判断是否需要更新
                    if (ability.getAbilityCenterParams() != null) {
                        String currentVersion = ability.getAbilityCenterParams().getVersion();
                        String latestVersion = optional.get().getReleaseVersion();
                        ability.setUpdateStatus(StringUtils.equals(currentVersion, latestVersion)
                            ? AbilityUpdateStatus.UPDATED : AbilityUpdateStatus.NEED_UPDATE);
                    } else {
                        // 如果没有能力中心信息，则添加
                        AbilityDTO dto = optional.get();
                        AbilityCenterCallParams params = new AbilityCenterCallParams(
                            dto.getAppId(), dto.getAppName(), dto.getPowerId(), dto.getReleaseVersion());
                        ability.setAbilityCenterParams(params);
                        abilityMapper.updateById(ability);
                    }
                }

            }
        });

        // 递归构建能力树
        buildAbilityTreeWithCategory(categoryTree, abilities);
        // 执行搜索
        if (request.getSearch() != null && !request.getSearch().isEmpty()) {
            List<TreeBaseResponse> searchResults = new ArrayList<>();
            for (TreeBaseResponse root : categoryTree) {
                searchResults.addAll(root.searchTree(request.getSearch()));
            }
            categoryTree = searchResults;
        }
        return categoryTree;
    }

    /**
     * 支持在批处理中使用的能力的目录树
     *
     * @return {@link List }<{@link TreeBaseResponse }>
     */
    @Override
    public List<TreeBaseResponse> getAvailableBatchAbilityTree() {
        List<TreeBaseResponse> categoryTree = operatorCategoryService.getAllOperatorCategoryList();
        SearchTypeArrayAbilityTreeRequest availableBatchAbilityRequest = new SearchTypeArrayAbilityTreeRequest();
        availableBatchAbilityRequest.setIsBatchSupported(Boolean.TRUE);
        List<Ability> abilities = abilityMapper.selectAllByRequest(availableBatchAbilityRequest);

        // 递归构建能力树
        buildAbilityTreeWithCategory(categoryTree, abilities);

        return categoryTree;
    }

    /**
     * 递归构建能力树
     *
     * @param categoryNodes 分类节点列表
     * @param abilities     所有能力列表
     */
    private void buildAbilityTreeWithCategory(List<TreeBaseResponse> categoryNodes, List<Ability> abilities) {
        if (categoryNodes == null || categoryNodes.isEmpty()) {
            return;
        }

        for (TreeBaseResponse node : categoryNodes) {
            // 判断是否为系统内置分类并设置编辑权限
            configureNodeEditPermission(node);

            // 处理子节点
            processNodeChildren(node, abilities);
        }
    }

    /**
     * 配置节点的编辑权限
     *
     * @param node 需要配置的节点
     */
    private void configureNodeEditPermission(TreeBaseResponse node) {
        // 判断是否为系统内置分类
        if (AbilityCategory.isSystemCategory(node.getId())) {
            node.setIsEnableEdit(false);
        }
    }

    /**
     * 处理节点的子节点
     *
     * @param node      当前处理的节点
     * @param abilities 所有能力列表
     */
    private void processNodeChildren(TreeBaseResponse node, List<Ability> abilities) {
        // 获取当前分类下的能力
        List<TreeBaseResponse> abilityNodes = getAbilityByCategory(abilities, node);

        // 获取当前节点的子分类
        List<TreeBaseResponse> childCategories = node.getChildrenAsTreeBaseResponse();

        // 如果父节点不可编辑，子分类也不可编辑
        if (Boolean.FALSE.equals(node.getIsEnableEdit())) {
            childCategories.forEach(child -> child.setIsEnableEdit(false));
        }

        // 如果有子分类，递归处理子分类
        if (ObjectUtils.isNotEmpty(childCategories)) {
            buildAbilityTreeWithCategory(childCategories, abilities);
        }

        // 合并子节点
        mergeChildNodes(node, childCategories, abilityNodes);
    }

    /**
     * 合并子节点
     *
     * @param node            当前节点
     * @param childCategories 子分类列表
     * @param abilityNodes    能力节点列表
     */
    private void mergeChildNodes(TreeBaseResponse node, List<TreeBaseResponse> childCategories,
        List<TreeBaseResponse> abilityNodes) {
        if (ObjectUtils.isNotEmpty(childCategories)) {
            if (ObjectUtils.isNotEmpty(abilityNodes)) {
                List<TreeBaseResponse> mergedChildren = new ArrayList<>(childCategories);
                mergedChildren.addAll(abilityNodes);
                node.setChildren(mergedChildren);
            } else {
                node.setChildren(childCategories);
            }
        } else {
            node.setChildren(abilityNodes);
        }
    }

    @Override
    public boolean abilityCheckName(NameTypeEnum type, String name) {
        if (NameTypeEnum.EN.equals(type)) {
            return abilityMapper.existByEnName(name);
        } else {
            return abilityMapper.existByZhName(name);
        }
    }

    @Override
    public void batchMoveAbility(List<Integer> operatorIds, Integer targetCategoryId) {
        abilityMapper.updateCategoryByOperatorIds(operatorIds, targetCategoryId);
    }

    @Override
    public void batchDeleteAbilities(List<Integer> ids) {
        for (Integer operatorId : ids) {
            checkAbilityExist(operatorId);
            UsageInfoResponse usage = getAbilityUsageInfo(operatorId);
            if (usage.isUse()) {
                throw new BizException(usage.getMessage());
            }
        }
        abilityMapper.deleteByIds(ids);
    }

    private void checkAbilityExist(Integer operatorId) {
        if (!abilityMapper.existById(operatorId)) {
            log.warn("id为{}的算子不存在！", operatorId);
        }
    }

    @Override
    public AbilityTestNewResponse testOperator(Integer id, AbilityTestRequest request) {
        AbilityTestNewResponse response = new AbilityTestNewResponse();
        if (Objects.isNull(request.getAbility())) {
            throw new IllegalArgumentException("能力不能为空！");
        }
        AbilityExecuteParams engineRequest = createEngineRequest(request);
        try {
            ResponseMessage engineResponse = streamEngineFeign.abilityExecute(engineRequest);
            response = buildAbilityTestResponse(request, engineResponse);
        } catch (Exception e) {
            log.error("能力测试失败：", e);
            response.setIsSuccess(false);
            response.setErrorMessage("Engine request error: " + e.getMessage());
        } finally {
            updateTestStatusInDb(id, response.getIsSuccess());
        }
        return response;
    }

    @Override
    public UsageInfoResponse getAbilityUsageInfo(Integer abilityId) {
        checkAbilityExist(abilityId);
        //查询使用算子的流处理
        List<KeyValueResponse> usingTables = operatorNewMapper.selectUsingDataModel(abilityId);
        //查询使用算子的批处理
        List<KeyValueResponse> usingBatchTables = batchOperatorMapper.selectUsingDataModel(abilityId);
        usingTables.addAll(usingBatchTables);
        Ability ability = abilityMapper.selectById(abilityId);
        return new UsageInfoResponse(List.of(new UsingObjects(ModuleEnum.DATA_MODELING, usingTables)),
            ability.getZhName(), ModuleEnum.OPERATOR);
    }

    @Override
    public AbilityDetailResponse getAbilityDetailInfo(Integer abilityId) {
        checkAbilityExist(abilityId);
        Ability ability = abilityMapper.selectById(abilityId);
        if (Objects.isNull(ability)) {
            throw new BizException("算子不存在！");
        }
        // 通过工具类去除转义
        //String unescapedMarkdown = MarkdownUnescapeUtils.unescapeMarkdownWithJson(ability.getDetails());
        return new AbilityDetailResponse(ability.getDetails());
    }

    private AbilityExecuteParams createEngineRequest(AbilityTestRequest request) {
        AbilityExecuteParams abilityExecuteParams = new AbilityExecuteParams();
        abilityExecuteParams.setAbilityId(request.getAbility().getId());
        abilityExecuteParams.setInput(JsonUtils.mapToJsonNode(request.getInput()));
        abilityExecuteParams.setInputBind(request.getInputBind());
        return abilityExecuteParams;
    }

    private AbilityTestNewResponse buildAbilityTestResponse(AbilityTestRequest request,
        ResponseMessage engineResponse) {
        AbilityTestNewResponse response = new AbilityTestNewResponse();
        if (Objects.isNull(engineResponse)) {
            response.setIsSuccess(false);
            response.setErrorMessage("stream-engine返回结果为空！");
            return response;
        }

        ResponseMessage resultResponse = JsonUtils.parseObject(
            JsonUtils.toJsonString(engineResponse.getData()), ResponseMessage.class);
        if (Objects.isNull(resultResponse)) {
            response.setIsSuccess(false);
            response.setErrorMessage("stream-engine返回结果为空！");
            return response;
        }
        Object resultData = resultResponse.getData();
        if (!resultResponse.isSuccess()) {
            response.setIsSuccess(false);
            response.setErrorMessage(resultResponse.getMessage());
            return response;
        }
        if (AbilityType.LOCAL == request.getAbility().getType()) {
            log.info("本地能力{}的测试结果: {}", request.getAbility().getPath(), resultData);
        } else {
            log.info("远程能力{}的测试结果: {}", request.getAbility().getHttpRequestConfig().getUrl(), resultData);
        }
        try {
            response.setResult(formatResult(resultData, request.getAbility().getOutputSchema()));
            response.setIsSuccess(true);
        } catch (Exception e) {
            response.setIsSuccess(false);
            response.setErrorMessage("结果格式化错误: " + e.getMessage());
        }
        return response;
    }

    private void updateTestStatusInDb(Integer id, Boolean isSuccess) {
        OperatorTestStatus status = Boolean.TRUE.equals(isSuccess)
            ? OperatorTestStatus.PASSED
            : OperatorTestStatus.FAILED;
        abilityMapper.updateTestStatus(id, status);
    }

    private String formatResult(Object result, Schema outputSchema) {
        if (result == null) {
            return "{}";
        }
        if (isJsonString(result)) {
            return JsonUtils.toJsonString(result);
        }
        return wrapInRootObject(result, outputSchema);
    }

    private boolean isJsonString(Object data) {
        try {
            JsonNode jsonNode = JsonUtils.parseJsonNode(JsonUtils.toJsonString(data));
            return jsonNode.isObject() || jsonNode.isArray();
        } catch (Exception e) {
            return false;
        }
    }

    private String wrapInRootObject(Object result, Schema outputSchema) {
        String resultKey = determineResultKey(outputSchema);
        return JsonUtils.toJsonString(Collections.singletonMap(resultKey, result));
    }

    private String determineResultKey(Schema outputSchema) {
        return (outputSchema != null && StringUtils.isNotBlank(outputSchema.getEnName()))
            ? outputSchema.getEnName()
            : "root";
    }

    private List<TreeBaseResponse> getAbilityByCategory(List<Ability> abilities, TreeBaseResponse categoryNode) {
        return abilities.stream()
            //存储算子不显示在算子树中
            .filter(ability -> !ability.getType().equals(AbilityType.STORAGE))
            .filter(ability -> categoryNode.getId().equals(ability.getOperatorCategoryId()))
            .map(ability -> {
                AbilityArrangementTreeResponse treeVO = new AbilityArrangementTreeResponse(ability);
                // 继承父节点的编辑权限
                treeVO.setIsEnableEdit(categoryNode.getIsEnableEdit());
                treeVO.setPid(categoryNode.getId());
                treeVO.setIconName(ability.getIconName());
                return treeVO;
            }).collect(Collectors.toCollection(ArrayList::new));
    }


    private Ability buildAbility(AbilityRequest request) {
        Ability ability = new Ability();
        ability.setEnName(request.getEnName());
        ability.setZhName(request.getZhName());
        ability.setDescription(request.getDescription());
        ability.setType(request.getType());
        ability.setPath(request.getPath());
        ability.setOperatorCategoryId(request.getOperatorCategoryId());
        ability.setIconName(request.getIconName());
        ability.setUpdateStatus(Optional.ofNullable(request.getUpdateStatus()).orElse(AbilityUpdateStatus.UPDATED));
        ability.setTestStatus(Optional.ofNullable(request.getTestStatus()).orElse(AbilityTestStatus.UNTESTED));
        ability.setInputSchema(request.getInputSchema() == null ? Schema.voidSchema() : request.getInputSchema());
        ability.setOutputSchema(request.getOutputSchema() == null ? Schema.voidSchema() : request.getOutputSchema());
        ability.setHttpRequestConfig(request.getHttpRequestConfig());
        ability.setLlmConfig(request.getLlmConfig());
        ability.setAbilityCenterParams(request.getAbilityCenterParams());
        return ability;
    }
}
