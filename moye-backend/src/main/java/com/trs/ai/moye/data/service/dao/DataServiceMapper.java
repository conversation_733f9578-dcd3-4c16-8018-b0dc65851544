package com.trs.ai.moye.data.service.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.ai.moye.data.service.dto.DataServiceDto;
import com.trs.ai.moye.data.service.entity.DataService;
import com.trs.ai.moye.data.service.enums.ServiceCreateMode;
import com.trs.ai.moye.data.service.enums.ServicePublishStatus;
import com.trs.ai.moye.data.service.request.CheckNameDataServiceRequest;
import com.trs.ai.moye.data.service.response.DataServiceOutResponse;
import com.trs.moye.base.common.request.SearchParams;
import com.trs.moye.base.common.request.SortParams;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * 数据服务分类mapper
 *
 * <AUTHOR>
 * @since 2024/09/24 15:04:27
 */
@Mapper
public interface DataServiceMapper extends BaseMapper<DataService> {

    /**
     * 检查名称重复
     *
     * @param checkNameParam 数据服务
     * @return boolean
     * <AUTHOR>
     * @since 2024/09/25 16:11:19
     */
    boolean checkName(@Param("checkNameParam") CheckNameDataServiceRequest checkNameParam);

    /**
     * 根据code查询
     *
     * @param code code
     * @return {@link DataService }
     * <AUTHOR>
     * @since 2024/09/27 11:20:32
     */
    DataService selectByCode(@Param("code") String code);

    /**
     * 根据id查询dto
     *
     * @param id id
     * @return {@link DataServiceDto }
     * <AUTHOR>
     * @since 2024/09/27 18:12:38
     */
    DataServiceDto selectDtoById(@Param("id") Integer id);

    /**
     * 根据id查询dto
     *
     * @param code code
     * @return {@link DataServiceDto }
     * <AUTHOR>
     * @since 2024/09/27 18:12:38
     */
    DataServiceDto selectDtoByCode(@Param("code") String code);

    /**
     * 根据id更新发布状态
     *
     * @param id            id
     * @param publishStatus 发布状态
     * <AUTHOR>
     * @since 2024/10/09 16:22:42
     */
    void updatePublishStatusById(@Param("id") Integer id, @Param("publishStatus") ServicePublishStatus publishStatus);


    /**
     * 通过存储点来查询数据服务
     *
     * @param storageIds 存储点id
     * @return {@link DataService}
     * <AUTHOR>
     * @since 2024/10/10 11:16
     */
    List<DataService> selectByStorageIds(@Param("storageIds") List<Integer> storageIds);

    /**
     * 通过存储点来查询数据服务
     *
     * @param storageIds 存储 ID
     * @return {@link List }<{@link DataServiceDto }>
     * <AUTHOR>
     * @since 2024/10/12 16:48:16
     */
    List<DataServiceDto> selectDtoByStorageIds(@Param("storageIds") List<Integer> storageIds);

    /**
     * 根据分类id查询数据服务
     *
     * @param categoryId 分类id
     * @return {@link List }<{@link DataService }>
     * <AUTHOR>
     * @since 2024/10/24 17:04:44
     */
    List<DataService> selectByCategoryId(@Param("categoryId") Integer categoryId);


    /**
     * 通过ID查询列表
     *
     * @param ids ID列表
     * @return {@link DataService}
     * <AUTHOR>
     * @since 2024/12/4 13:52
     */
    List<DataService> selectByIds(@Param("ids") List<Integer> ids);

    /**
     * 通过ID修改层级ID
     *
     * @param categoryId 层级ID
     * @param ids        主键ID
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/12/4 14:48
     */
    boolean updateCategoryIdByIds(@Param("categoryId") Integer categoryId, @Param("ids") Set<Integer> ids);

    /**
     * 查询数据服务 对外
     *
     * @param businessIds 业务id
     * @param searchable  查询参数
     * @param sortable    排序参数
     * @param mode        模式
     * @param page        分页参数
     * @return 分页后的数据服务
     */
    Page<DataServiceOutResponse> selectByCategoryIds(
        @Param("businessIds") List<Integer> businessIds,
        @Param("searchRequest") SearchParams searchable,
        @Param("sortable") SortParams sortable,
        @Param("mode") ServiceCreateMode mode,
        Page<DataServiceOutResponse> page);

    /**
     * 根据业务分类id查询数据服务DTO列表
     *
     * @param businessCategoryId 业务分类id
     * @return List of {@link DataServiceDto}
     */
    List<DataServiceDto> selectDtoByBusinessCategoryId(@Param("businessCategoryId") Integer businessCategoryId);

    /**
     * 根据名称查询数据服务
     *
     * @param name 名称
     * @return {@link DataService}
     */
    DataService selectByName(@Param("name") String name);
}
