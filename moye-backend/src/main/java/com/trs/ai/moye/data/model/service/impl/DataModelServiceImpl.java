package com.trs.ai.moye.data.model.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.clickhouse.client.internal.google.common.util.concurrent.ThreadFactoryBuilder;
import com.trs.ai.moye.backstage.dao.NoticeSendConfInsideMapper;
import com.trs.ai.moye.backstage.dao.UserMapper;
import com.trs.ai.moye.backstage.entity.User;
import com.trs.ai.moye.bi.dao.VisualAnalysisSubjectMapper;
import com.trs.ai.moye.bi.domain.entity.VisualAnalysisIdName;
import com.trs.ai.moye.bi.service.VisualAnalysisChartHelper;
import com.trs.ai.moye.common.entity.UsageInfoResponse;
import com.trs.ai.moye.common.entity.UsageInfoResponse.UsingObjects;
import com.trs.ai.moye.common.response.TreeBaseResponse;
import com.trs.ai.moye.common.utils.BeanUtil;
import com.trs.ai.moye.common.utils.BizUtils;
import com.trs.ai.moye.data.model.dao.BatchArrangementMapper;
import com.trs.ai.moye.data.model.dao.BatchTaskRecordMapper;
import com.trs.ai.moye.data.model.dao.DataModelDisplayMapper;
import com.trs.ai.moye.data.model.dao.DataModelMonitorConfigDisplayMapper;
import com.trs.ai.moye.data.model.dao.DataModelViewInfoMapper;
import com.trs.ai.moye.data.model.dao.DataProcessMonitorConfigMapper;
import com.trs.ai.moye.data.model.dao.ProcessMapper;
import com.trs.ai.moye.data.model.dao.StorageTaskMapper;
import com.trs.ai.moye.data.model.dto.ModelConnectionTypeDTO;
import com.trs.ai.moye.data.model.dto.ReverseModelDTO;
import com.trs.ai.moye.data.model.dto.StoragePointConnection;
import com.trs.ai.moye.data.model.dto.StorageTable;
import com.trs.ai.moye.data.model.dto.arrangement.stream.OperatorPipelineDTO;
import com.trs.ai.moye.data.model.entity.BatchArrangement;
import com.trs.ai.moye.data.model.entity.MonitorVersionQuery;
import com.trs.ai.moye.data.model.entity.SourceDataMode;
import com.trs.ai.moye.data.model.entity.StorageTask;
import com.trs.ai.moye.data.model.entity.view.DataModelViewInfo;
import com.trs.ai.moye.data.model.enums.ArrangeDisplayType;
import com.trs.ai.moye.data.model.enums.ProcessType;
import com.trs.ai.moye.data.model.feign.McpServiceFeign;
import com.trs.ai.moye.data.model.request.AllMonitorConfigRequest;
import com.trs.ai.moye.data.model.request.CreateTableRequests;
import com.trs.ai.moye.data.model.request.CreateTableRequests.CreateTableRequest;
import com.trs.ai.moye.data.model.request.DataModelUpdateRequest;
import com.trs.ai.moye.data.model.request.DataSourceModelRequest;
import com.trs.ai.moye.data.model.request.FieldExportRequest;
import com.trs.ai.moye.data.model.request.ImmediateExecuteRequest;
import com.trs.ai.moye.data.model.request.ReadErrorMessagesRequest;
import com.trs.ai.moye.data.model.request.ScheduleRecordRequest;
import com.trs.ai.moye.data.model.request.ods.OdsFieldRequest;
import com.trs.ai.moye.data.model.response.BatchDeleteResponse;
import com.trs.ai.moye.data.model.response.CreateTableResponse;
import com.trs.ai.moye.data.model.response.DataModelSourceDetailResponse;
import com.trs.ai.moye.data.model.response.DataModelStatusResponse;
import com.trs.ai.moye.data.model.response.DataSourceModelResponse;
import com.trs.ai.moye.data.model.response.DwdRealtimeStatusResponse;
import com.trs.ai.moye.data.model.response.FieldPageListResponse;
import com.trs.ai.moye.data.model.response.KeyValueResponse;
import com.trs.ai.moye.data.model.response.ModelBasicInfoResponse;
import com.trs.ai.moye.data.model.response.ModelDataStorageResponse;
import com.trs.ai.moye.data.model.response.ModelMonitorConfigResponse;
import com.trs.ai.moye.data.model.response.MonitorConfigVersionResponse;
import com.trs.ai.moye.data.model.response.StorageEngineResponse;
import com.trs.ai.moye.data.model.service.BatchArrangementService;
import com.trs.ai.moye.data.model.service.DataAccessMonitorConfigService;
import com.trs.ai.moye.data.model.service.DataModelExecuteStatusService;
import com.trs.ai.moye.data.model.service.DataModelService;
import com.trs.ai.moye.data.model.service.DataProcessMonitorConfigService;
import com.trs.ai.moye.data.model.service.OperatorPipelineService;
import com.trs.ai.moye.data.model.task.start.StreamProcessHelper;
import com.trs.ai.moye.data.service.dao.DataServiceMapper;
import com.trs.ai.moye.data.service.entity.DataService;
import com.trs.ai.moye.data.service.enums.ServiceConfigType;
import com.trs.ai.moye.data.standard.export.ExportStrategy;
import com.trs.ai.moye.data.standard.export.ExportStrategyContext;
import com.trs.ai.moye.storageengine.request.ConditionSearchParams;
import com.trs.ai.moye.storageengine.request.FileColumnRequest;
import com.trs.ai.moye.storageengine.request.SubmitJobRequest;
import com.trs.ai.moye.storageengine.response.RestfulResponse;
import com.trs.ai.moye.storageengine.response.StorageSearchResponse;
import com.trs.ai.moye.storageengine.service.StorageEngineService;
import com.trs.ai.moye.xxljob.XXLJobManager;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import com.trs.moye.base.common.entity.ModuleEnum;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.help.RepeatChecker;
import com.trs.moye.base.common.request.PageParams;
import com.trs.moye.base.common.request.TimeRangeParams;
import com.trs.moye.base.common.response.IdNameResponse;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.utils.AssertUtils;
import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.connection.enums.DataSourceCategory;
import com.trs.moye.base.data.execute.ExecuteModeEnum;
import com.trs.moye.base.data.indicator.dao.IndicatorConfigMapper;
import com.trs.moye.base.data.indicator.dao.IndicatorFieldMapper;
import com.trs.moye.base.data.model.dao.AggregationTableArrangementMapper;
import com.trs.moye.base.data.model.dao.BusinessCategoryMapper;
import com.trs.moye.base.data.model.dao.DataModelExecuteConfigMapper;
import com.trs.moye.base.data.model.dao.DataModelFieldMapper;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.dao.DataModelScheduleConfigMapper;
import com.trs.moye.base.data.model.entity.AbstractField;
import com.trs.moye.base.data.model.entity.BusinessCategory;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelExecuteConfig;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.entity.DataModelScheduleConfig;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.model.enums.CreateModeEnum;
import com.trs.moye.base.data.model.enums.CreateTableStatus;
import com.trs.moye.base.data.model.enums.DataProcessMode;
import com.trs.moye.base.data.model.enums.ModelExecuteStatus;
import com.trs.moye.base.data.model.field.mapping.MoyeFieldResponse;
import com.trs.moye.base.data.source.dao.DataSourceConfigMapper;
import com.trs.moye.base.data.source.setting.DataSourceSettings;
import com.trs.moye.base.data.source.setting.DataSourceSettings.DefaultDataSourceSettings;
import com.trs.moye.base.data.source.setting.file.FtpDataSourceSettings;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.IncrementInfo;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import com.trs.moye.base.data.storage.nebula.ChangeFieldNameDto;
import com.trs.moye.base.data.storage.setting.DataStorageSettings;
import com.trs.moye.base.data.storage.setting.DefaultDataStorageSettings;
import com.trs.moye.base.monitor.dao.DataModelMonitorConfigMapper;
import com.trs.moye.base.monitor.entity.DataModelMonitorConfig;
import com.trs.moye.base.monitor.entity.DataProcessMonitorConfig;
import com.trs.moye.base.monitor.enums.DataProcessMonitorType;
import com.trs.moye.base.monitor.enums.MonitorConfigType;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * 数据建模服务类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/9/26 14:17
 **/
@Service
@Slf4j
public class DataModelServiceImpl implements DataModelService {

    public static final String MSG_DATA_MODEL_NOT_EXIST = "主键为【%s】的数据建模不存在";

    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private BusinessCategoryMapper businessCategoryMapper;
    @Resource
    private DataModelMapper dataModelMapper;
    @Resource
    private DataModelDisplayMapper dataModelDisplayMapper;
    @Resource
    private UserMapper userMapper;
    @Resource
    private DataSourceConfigMapper dataSourceConfigMapper;
    @Resource
    private DataModelExecuteConfigMapper dataModelExecuteConfigMapper;
    @Resource
    private DataModelScheduleConfigMapper dataModelScheduleConfigMapper;
    @Resource
    private DataModelMonitorConfigMapper dataModelMonitorConfigMapper;
    @Resource
    private DataProcessMonitorConfigMapper dataProcessMonitorConfigMapper;
    @Resource
    private DataModelMonitorConfigDisplayMapper dataModelMonitorConfigDisplayMapper;
    @Resource
    private DataModelFieldMapper dataModelFieldMapper;
    @Resource
    private DataStorageMapper dataStorageMapper;
    @Resource
    private DataServiceMapper dataServiceMapper;
    @Resource
    private StorageEngineService storageEngineService;
    @Resource
    private XXLJobManager xxlJobManager;
    @Resource
    private OperatorPipelineService operatorPipelineService;
    @Resource
    private BatchArrangementService batchArrangementService;
    @Resource
    private DataModelExecuteStatusService dataModelExecuteStatusService;
    @Resource
    private StorageTaskMapper storageTaskMapper;
    @Resource
    private ProcessMapper processMapper;
    @Resource
    private BatchTaskRecordMapper batchTaskRecordMapper;
    @Resource
    private BatchArrangementMapper batchArrangementMapper;
    @Resource
    private ExportStrategyContext exportStrategyContext;
    @Resource
    private DataModelViewInfoMapper dataModelViewInfoMapper;
    @Resource
    private DataConnectionMapper dataConnectionMapper;
    @Resource
    private NoticeSendConfInsideMapper noticeSendConfInsideMapper;
    @Resource
    private DataAccessMonitorConfigService dataModelConfigService;
    @Resource
    private DataProcessMonitorConfigService dataProcessMonitorConfigService;
    @Resource
    private VisualAnalysisSubjectMapper visualAnalysisSubjectMapper;
    @Resource
    private IndicatorConfigMapper indicatorConfigMapper;
    @Resource
    private DataModelFieldService dataModelFieldService;
    @Resource
    private McpServiceFeign mcpServiceFeign;
    @Resource
    private IndicatorFieldMapper indicatorFieldMapper;
    @Resource
    private StreamProcessHelper streamProcessHelper;
    @Resource
    private VisualAnalysisChartHelper visualAnalysisChartHelper;
    @Resource
    private AggregationTableArrangementMapper aggregationTableArrangementMapper;

    /**
     * 批量删除操作使用的线程池
     * <br>
     * 批量删除应该是个低频操作，同时一次操作中批量删除数量大概率是5个左右，可能会达到20个
     * <p>
     * 使用该线程池的 批量删除方法
     */
    private final ExecutorService batchDeleteExecutorService = new ThreadPoolExecutor(0, 10, 10L, TimeUnit.SECONDS,
        new LinkedBlockingQueue<>(100), new ThreadFactoryBuilder().setNameFormat("delete-modeling-%s").build(),
        new ThreadPoolExecutor.CallerRunsPolicy());


    @Override
    public ModelBasicInfoResponse getModelBasicInfo(Integer id) {
        DataModel dataModel = dataModelMapper.selectById(id);
        AssertUtils.notEmpty(dataModel, MSG_DATA_MODEL_NOT_EXIST, id);
        ModelBasicInfoResponse response = new ModelBasicInfoResponse(dataModel);
        // 设置业务分类
        BusinessCategory businessCategory = businessCategoryMapper.selectById(dataModel.getBusinessCategoryId());
        response.setBusinessCategory(new IdNameResponse(businessCategory.getId(), businessCategory.getZhName()));
        // 设置创建人
        if (ObjectUtils.isNotEmpty(dataModel.getCreateBy())) {
            User createUser = userMapper.selectById(dataModel.getCreateBy());
            response.setCreateUser(new IdNameResponse(createUser.getId(), createUser.getName()));
        }
        // 设置更新人
        if (ObjectUtils.isNotEmpty(dataModel.getUpdateBy())) {
            User updateUser = userMapper.selectById(dataModel.getUpdateBy());
            response.setUpdateUser(new IdNameResponse(updateUser.getId(), updateUser.getName()));
        }
        return response;
    }

    @Override
    public void updateModelZhName(Integer id, DataModelUpdateRequest request) {
        DataModel dataModel = dataModelMapper.getById(id);
        AssertUtils.notEmpty(dataModel, MSG_DATA_MODEL_NOT_EXIST, id);
        dataModel.setZhName(request.getZhName());
        dataModel.setDescription(request.getDescription());
        dataModelMapper.updateById(dataModel);
    }

    @Override
    public void updateIsPublishToMcp(Integer id, Boolean isPublish) {
        DataModel dataModel = dataModelMapper.getById(id);
        AssertUtils.notEmpty(dataModel, MSG_DATA_MODEL_NOT_EXIST, id);
        dataModel.setIsMcpPublished(isPublish);
        dataModelMapper.updateById(dataModel);
        //通知mcp服务更新
        try {
            mcpServiceFeign.updateResources(id, isPublish);
        } catch (Exception e) {
            log.error("更新MCP服务资源失败, id: {}", id, e);
        }
    }

    private DataSourceConfig getOneDataSource(Integer id) {
        DataModel dataModel = dataModelMapper.selectById(id);
        AssertUtils.notEmpty(dataModel, MSG_DATA_MODEL_NOT_EXIST, id);
        DataSourceConfig dataSource = dataSourceConfigMapper.selectOneByDataModelId(dataModel.getId());
        AssertUtils.notEmpty(dataSource, "【%s】数据建模的数据源不存在", BizUtils.getDataModelIdentity(dataModel));
        return dataSource;
    }

    @Override
    public List<ModelDataStorageResponse> getModelDataStorages(Integer id) {
        DataModel dataModel = dataModelMapper.selectById(id);
        AssertUtils.notEmpty(dataModel, MSG_DATA_MODEL_NOT_EXIST, id);
        Set<Integer> modelFieldIds = dataModelFieldMapper.listByDataModelId(id).stream().map(AuditBaseEntity::getId)
            .collect(Collectors.toSet());
        return dataStorageMapper.selectByDataModelIdWithConnection(id).stream().map(storage -> {
            ModelDataStorageResponse response = new ModelDataStorageResponse(storage);
            response.setUsageInfo(getStorageUsageInfo(storage));
            response.setExistNotCreateTableField(isExistNotCreateTableField(modelFieldIds, storage));
            return response;
        }).toList();
    }

    private boolean isExistNotCreateTableField(Set<Integer> modelFieldIds, DataStorage storage) {
        if (ObjectUtils.isEmpty(modelFieldIds)) {
            return false;
        }
        if (ObjectUtils.isEmpty(storage.getFieldIds())) {
            return true;
        }
        return storage.getFieldIds().size() != modelFieldIds.size() || !new HashSet<>(
            storage.getFieldIds()).containsAll(modelFieldIds);
    }


    /**
     * 获取存储使用的使用信息
     *
     * @param storage 存储
     * @return 使用的数据服务信息
     */
    private UsageInfoResponse getStorageUsageInfo(DataStorage storage) {
        Integer storageId = storage.getId();
        List<UsingObjects> usageDetailList = new ArrayList<>();
        // 使用存储点的数据服务
        UsingObjects usageDataServiceInfo = getStorageUsageDataServiceInfo(Collections.singletonList(storageId));
        if (ObjectUtils.isNotEmpty(usageDataServiceInfo)) {
            usageDetailList.add(usageDataServiceInfo);
        }
        UsingObjects usingCharts = visualAnalysisChartHelper.getStoragePointUsingCharts(storageId);
        if (ObjectUtils.isNotEmpty(usingCharts.getObjects())) {
            usageDetailList.add(usingCharts);
        }
        UsingObjects modelUsingObjects = new UsingObjects(ModuleEnum.DATA_MODELING);
        usageDetailList.add(modelUsingObjects);
        // 批处理建模
        UsingObjects batchUsingObjects = batchArrangementService.getStorageUsage(storageId);
        if (ObjectUtils.isNotEmpty(batchUsingObjects.getObjects())) {
            modelUsingObjects.getObjects().addAll(batchUsingObjects.getObjects());
        }
        List<DataModel> usingStreamProcessModelList = getUsingStreamProcessModelList(storage);
        if (ObjectUtils.isNotEmpty(usingStreamProcessModelList)) {
            modelUsingObjects.getObjects().addAll(
                usingStreamProcessModelList.stream()
                    .map(model -> new KeyValueResponse(model.getId(), model.getZhName()))
                    .toList());
        }
        return new UsageInfoResponse(usageDetailList, storage.getEnName(), ModuleEnum.DATA_STORAGE);
    }

    private List<DataModel> getUsingStreamProcessModelList(DataStorage storage) {
        // 获取来源表使用该存储点的流处理
        List<DataModel> sourceDataModels = dataModelMapper.selectByDataModelSourceId(storage.getDataModelId())
            .stream()
            .filter(model -> {
                DataModelScheduleConfig scheduleConfig = model.getScheduleConfig();
                return scheduleConfig != null && scheduleConfig.getExecuteMode() == ExecuteModeEnum.REALTIME;
            })
            .filter(model -> {
                DataStorage storagePoint = streamProcessHelper.getStreamProcessSourceModelStoragePoint(
                    model);
                return storage.getId().equals(storagePoint.getId());
            })
            .collect(Collectors.toList());
        // 获取存储表使用该存储点的流处理
        OperatorPipelineDTO pipeline = operatorPipelineService.getOperatorPipeline(storage.getDataModelId());
        if (pipeline != null && pipeline.getStorageIds().contains(storage.getId())) {
            sourceDataModels.add(dataModelMapper.selectById(storage.getDataModelId()));
        }
        return sourceDataModels;
    }


    @Override
    public List<TreeBaseResponse> getModelDataSourceTreeList(DataSourceModelRequest request) {
        // 获取数据源模型列表
        List<DataSourceModelResponse> dataSourceModelList = getModelDataSourceList(request);

        // 获取业务分类列表
        List<BusinessCategory> businessCategories = businessCategoryMapper.selectList(null);

        // 构建业务树(TreeBaseResponse) 第一层是BusinessCategory 第二层是过滤后的List<DataSourceModelResponse>
        return businessCategories.stream().map(businessCategory -> {
            TreeBaseResponse treeBaseResponse = new TreeBaseResponse();
            treeBaseResponse.setId(businessCategory.getId());
            treeBaseResponse.setName(businessCategory.getZhName());
            treeBaseResponse.setChildren(dataSourceModelList.stream().filter(
                    dataSourceModelResponse -> dataSourceModelResponse.getCategoryId().equals(businessCategory.getId()))
                .collect(Collectors.toList()));
            return treeBaseResponse;
        }).collect(Collectors.toList());
    }

    @Override
    public List<DataSourceModelResponse> getModelDataSourceList(DataSourceModelRequest request) {
        Map<Integer, Set<ConnectionType>> storageCategoryDictMap = dataModelDisplayMapper.selectAllModelStorageDictList()
            .stream().collect(Collectors.groupingBy(ModelConnectionTypeDTO::getDataModelId,
                Collectors.mapping(ModelConnectionTypeDTO::getConnectionType, Collectors.toSet())));
        Map<Integer, ConnectionType> connTypeMap = dataConnectionMapper.selectAllConnection().stream()
            .collect(Collectors.toMap(DataConnection::getId, DataConnection::getConnectionType));

        // 获取原始数据建模列表
        List<SourceDataMode> sourceDataModes = dataModelDisplayMapper.selectSourceDataModelList(request);

        // 批量预查询存储点建表状态（仅在需要过滤时执行）
        Map<Integer, Boolean> builtTableStatusMap = new HashMap<>();
        if (Objects.nonNull(request.getFilterNotCreatedTables()) && request.getFilterNotCreatedTables()) {
            List<Integer> dataModelIds = sourceDataModes.stream()
                .map(SourceDataMode::getId)
                .toList();
            builtTableStatusMap = batchCheckBuiltTableStatus(dataModelIds);
        }

        // 过滤调度方式，当dataProcessMode为空时不进行过滤
        Predicate<DataSourceModelResponse> filter = Objects.isNull(request.getDataProcessMode())
            ? obj -> true  // 不进行过滤，返回所有结果
            : request.getDataProcessMode() == DataProcessMode.REALTIME
                ? obj -> obj.getStoragePointTypes().stream()
                .anyMatch(type -> type.getCategory() == DataSourceCategory.MQ)
                : obj -> obj.getStoragePointTypes().stream()
                    .anyMatch(type -> type.getCategory() != DataSourceCategory.MQ);

        // 过滤未建表的数据建模（使用预查询的缓存数据）
        final Map<Integer, Boolean> finalBuiltTableStatusMap = builtTableStatusMap;
        Predicate<DataSourceModelResponse> tableFilter = Objects.isNull(request.getFilterNotCreatedTables())
            || !request.getFilterNotCreatedTables()
            ? obj -> true  // 不进行过滤，返回所有结果
            : obj -> finalBuiltTableStatusMap.getOrDefault(obj.getId(), false);  // 使用缓存的建表状态

        //要素库的数据来源只有贴源库：一部分查询条件通过sql实现，一部分代码实现
        return sourceDataModes.stream()
            .map(obj -> DataSourceModelResponse.from(obj, storageCategoryDictMap, connTypeMap,
                dataModelExecuteStatusService))
            .filter(filter)
            .filter(tableFilter)
            .filter(
                res -> Objects.isNull(request.getConnectionType()) || res.getStoragePointTypes().stream()
                    .anyMatch(e -> request.getConnectionType().equals(e)))
            .toList();
    }

    /**
     * 批量检查数据建模的建表状态
     *
     * @param dataModelIds 数据建模ID列表
     * @return 数据建模ID到建表状态的映射，key为数据建模ID，value为是否至少有一个存储点已建表
     */
    private Map<Integer, Boolean> batchCheckBuiltTableStatus(List<Integer> dataModelIds) {
        if (dataModelIds == null || dataModelIds.isEmpty()) {
            return new HashMap<>();
        }
        List<DataStorage> allStorages = dataStorageMapper.selectByDataModelIds(dataModelIds);
        // 按数据建模ID分组存储点
        Map<Integer, List<DataStorage>> storageMap = allStorages.stream()
            .collect(Collectors.groupingBy(DataStorage::getDataModelId));

        // 检查每个数据建模是否至少有一个存储点已建表
        Map<Integer, Boolean> resultMap = new HashMap<>();
        for (Integer dataModelId : dataModelIds) {
            List<DataStorage> storages = storageMap.get(dataModelId);
            boolean hasBuiltTable = storages != null && storages.stream()
                .anyMatch(storage -> CreateTableStatus.SUCCESS.equals(storage.getCreateTableStatus()));
            resultMap.put(dataModelId, hasBuiltTable);
        }
        return resultMap;
    }

    @Override
    public ModelMonitorConfigResponse getModelMonitorConfigInfo(Integer id) {
        DataModel dataModel = dataModelMapper.selectById(id);
        AssertUtils.notEmpty(dataModel, MSG_DATA_MODEL_NOT_EXIST, id);
        ModelMonitorConfigResponse monitorConfigResponse = new ModelMonitorConfigResponse();
        setDataAccessMonitorConfig(id, monitorConfigResponse);
        setDataProcessMonitorConfig(id, monitorConfigResponse);
        return monitorConfigResponse;
    }

    private void setDataAccessMonitorConfig(Integer id, ModelMonitorConfigResponse monitorConfigResponse) {
        List<DataModelMonitorConfig> monitorConfigList = dataModelMonitorConfigMapper.dataModeInuseConfigList(id);
        if (ObjectUtils.isEmpty(monitorConfigList)) {
            // 建模创建时，监控配置为空，如果监控配置为空，直接响应一个空结果
            return;
        }
        RepeatChecker.checkCollection(monitorConfigList, "以下监控配置类型存在重复：\n",
            DataModelMonitorConfig::getType);
        Map<MonitorConfigType, DataModelMonitorConfig> monitorConfigMap = monitorConfigList.stream()
            .collect(Collectors.toMap(DataModelMonitorConfig::getType, Function.identity()));
        monitorConfigResponse.setLag(monitorConfigMap.get(MonitorConfigType.LAG));
        monitorConfigResponse.setFluctuation(monitorConfigMap.get(MonitorConfigType.FLUCTUATION));
        monitorConfigResponse.setCutoff(monitorConfigMap.get(MonitorConfigType.CUTOFF));
        monitorConfigResponse.setTaskExecutionTime(monitorConfigMap.get(MonitorConfigType.TASK_EXECUTION_TIME));
    }

    private void setDataProcessMonitorConfig(Integer id, ModelMonitorConfigResponse monitorConfigResponse) {
        List<DataProcessMonitorConfig> monitorConfigList = dataProcessMonitorConfigMapper.listByDataModelId(id);
        if (ObjectUtils.isEmpty(monitorConfigList)) {
            // 建模创建时，监控配置为空，如果监控配置为空，直接响应一个空结果
            return;
        }
        RepeatChecker.checkCollection(monitorConfigList, "以下监控配置类型存在重复：\n",
            DataProcessMonitorConfig::getType);
        Map<DataProcessMonitorType, DataProcessMonitorConfig> monitorConfigMap = monitorConfigList.stream()
            .collect(Collectors.toMap(DataProcessMonitorConfig::getType, Function.identity()));
        monitorConfigResponse.setStreamProcess(monitorConfigMap.get(DataProcessMonitorType.STREAM_PROCESS_V1));
        monitorConfigResponse.setOdsConfig(monitorConfigMap.get(DataProcessMonitorType.ODS_COMPLETE_LOG));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateMonitorConfigInfo(Integer id, AllMonitorConfigRequest request) {
        List<DataModelMonitorConfig> dataAccessMonitorConfigList = request.toMonitorConfigList(id);
        List<DataProcessMonitorConfig> dataProcessMonitorConfigList = request.toDataProcessMonitorConfigList(id);
        if (ObjectUtils.isNotEmpty(dataAccessMonitorConfigList)) {
            dataAccessMonitorConfigList.forEach(dataModelConfigService::updateMonitorConfigInfo);
        }
        if (ObjectUtils.isNotEmpty(dataProcessMonitorConfigList)) {
            dataProcessMonitorConfigList.forEach(dataProcessMonitorConfigService::updateMonitorConfigInfo);
        }
    }


    @Override
    public PageResponse<MonitorConfigVersionResponse> monitorTypeVersionPageList(Integer id,
        MonitorVersionQuery query) {
        DataModel dataModel = dataModelMapper.selectById(id);
        AssertUtils.notEmpty(dataModel, MSG_DATA_MODEL_NOT_EXIST, id);
        Page<DataModelMonitorConfig> page = dataModelMonitorConfigDisplayMapper.monitorTypeVersionPageList(id, query,
            query.getPageParams().toPage());
        Set<Integer> userIdSet = new HashSet<>();
        for (DataModelMonitorConfig vo : page.getRecords()) {
            if (vo.getUpdateBy() != null) {
                userIdSet.add(vo.getUpdateBy());
            }
        }
        Map<Integer, String> userNameMap = ObjectUtils.isEmpty(userIdSet) ? new HashMap<>()
            : userMapper.selectBatchIds(userIdSet).stream().collect(Collectors.toMap(User::getId, User::getName));
        return PageResponse.of(page).toNewPageResult(monitorConfig -> {
            MonitorConfigVersionResponse response = new MonitorConfigVersionResponse(monitorConfig);
            response.setUpdateUserName(userNameMap.get(monitorConfig.getCreateBy()));
            return response;
        });
    }

    @Override
    public DataModelStatusResponse modelStatus(Integer id) {
        DataModel dataModel = dataModelMapper.selectById(id);
        AssertUtils.notEmpty(dataModel, MSG_DATA_MODEL_NOT_EXIST, id);
        DataModelExecuteConfig executeConfig = dataModelExecuteConfigMapper.selectByDataModelId(id);
        DataModelStatusResponse response = new DataModelStatusResponse();
        response.setIsMcpPublished(dataModel.getIsMcpPublished());
        response.setExecuteParams(executeConfig == null ? null : executeConfig.getExecuteParams());
        response.setLayer(dataModel.getLayer());
        response.setExecuteStatus(dataModel.getExecuteStatus());
        response.setTableCreated(areAllStoragePointsTableCreated(id));
        DataModelScheduleConfig scheduleConfig = dataModelScheduleConfigMapper.selectByDataModelId(id);
        response.setScheduleCreated(Objects.nonNull(scheduleConfig));
        BatchArrangement batchArrangement = batchArrangementMapper.selectByDataModelId(id);
        ArrangeDisplayType arrangeDisplayType =
            batchArrangement == null ? null : batchArrangement.getDisplayType();
        response.setOperationMode(arrangeDisplayType);
        if (Objects.isNull(scheduleConfig)) {
            response.setProcessType(ProcessType.BATCH);
        } else {
            //实时就是流处理,如果是流处理，需要返回连接类型
            ProcessType processType =
                ExecuteModeEnum.REALTIME.equals(scheduleConfig.getExecuteMode()) ? ProcessType.STREAM
                    : ProcessType.BATCH;
            if (ProcessType.STREAM.equals(processType)) {
                DwdRealtimeStatusResponse realtimeStatusResponse = new DwdRealtimeStatusResponse();
                BeanUtil.copyInheritProperties(response, realtimeStatusResponse);
                if (dataModel.getLayer() == ModelLayer.ODS) {
                    List<DataSourceConfig> dataSources = dataModel.getDataSource();
                    if (CollectionUtils.isNotEmpty(dataSources) && dataSources.get(0).getConnection() != null) {
                        realtimeStatusResponse.setConnectionType(
                            dataSources.get(0).getConnection().getConnectionType());
                    }
                } else {
                    DataStorage storagePoint = streamProcessHelper.getStreamProcessSourceModelStoragePoint(
                        dataModel);
                    ConnectionType connectionType = storagePoint.getConnection().getConnectionType();
                    realtimeStatusResponse.setConnectionType(connectionType);
                }
                response = realtimeStatusResponse;
            }
            response.setProcessType(processType);
            response.setExecuteMode(scheduleConfig.getExecuteMode());
            //返回代码模式还是DAG模式;流处理默认为DAG模式
            if (ExecuteModeEnum.REALTIME.equals(scheduleConfig.getExecuteMode())) {
                response.setOperationMode(ArrangeDisplayType.CANVAS);
            }
            //返回是否可以启动
            response.setStartable(
                getCanStartStatus(dataModel, response.getOperationMode(), scheduleConfig.getExecuteMode()));
        }
        return response;
    }


    /**
     * 判断要素表是否能启动 判断条件：批处理-代码模式：不判断，任何情况下都可以启动 批处理-dag：有已发布的存储且arrangeConfig有算子编排时可以启动
     * 流处理：有已发布的存储且operatorInfo有算子编排时可以启动
     *
     * @param dataModel          数据模型
     * @param arrangeDisplayType 编排类型
     * @param executeMode        执行模式
     * @return Boolean
     */
    private Boolean getCanStartStatus(DataModel dataModel, ArrangeDisplayType arrangeDisplayType,
        ExecuteModeEnum executeMode) {
        Integer id = dataModel.getId();
        List<DataStorage> dataStorages = dataStorageMapper.selectByDataModelId(id);
        //判断是否有存储、建表
        boolean havePublishStorage = isHavePublishStorage(dataStorages);
        //如果是贴源库就只检查有没有存储，是否建表
        ModelLayer layer = dataModel.getLayer();
        if (ModelLayer.ODS.equals(layer)) {
            return havePublishStorage;
        } else if (ModelLayer.INDICATOR.equals(layer)) {
            // TODO 暂时直接允许指标库启动；后续改为和ods一致只判断存储
            return Boolean.TRUE;
        } else {
            //要素库批处理，判断是否存储、建表、和治理,代码模式不需要这些条件
            if (ArrangeDisplayType.CODE.equals(arrangeDisplayType)) {
                //查询治理状态
                return Boolean.TRUE;
            }
            boolean isStorageCreateTableArranged =
                havePublishStorage && Boolean.TRUE.equals(dataModel.getIsArranged()) ? Boolean.TRUE : Boolean.FALSE;
            // 流处理还需要判断来源存储点是否建表
            if (executeMode == ExecuteModeEnum.REALTIME) {
                DataStorage storagePoint = streamProcessHelper.getStreamProcessSourceModelStoragePoint(
                    dataModel);
                return isStorageCreateTableArranged && storagePoint.getCreateTableStatus() == CreateTableStatus.SUCCESS;
            } else {
                return isStorageCreateTableArranged;
            }
        }
    }

    private boolean isHavePublishStorage(List<DataStorage> storages) {
        if (storages == null || storages.isEmpty()) {
            return false;
        }
        return storages.stream().anyMatch(info -> CreateTableStatus.SUCCESS.equals(info.getCreateTableStatus()));
    }

    private boolean areAllStoragePointsTableCreated(Integer id) {
        for (DataStorage storage : dataStorageMapper.selectByDataModelId(id)) {
            if (storage.getCreateTableStatus() != CreateTableStatus.SUCCESS) {
                return false;
            }
        }
        return true;
    }

    @Override
    public List<FieldPageListResponse> modelFieldPageList(Integer id) {
        DataModel dataModel = dataModelMapper.getById(id);
        AssertUtils.notEmpty(dataModel, MSG_DATA_MODEL_NOT_EXIST, id);
        ModelLayer layer = dataModel.getLayer();
        if (ModelLayer.ODS.equals(layer)
            && !dataModel.isSyncField()
            && !dataModel.isFileType()
            && !dataModel.isApiType()) { //http类型不需要同步字段
            syncOdsFields(id);
        }
        Set<Integer> createFieldIds = getModelCreateTableFieldIds(id);
        // 使用 dataModelFieldService 的方法获取包含知识库信息的字段列表
        return dataModelFieldService.getModelFieldListWithKnowledgeBase(id, null).stream()
            .peek(field -> field.setCreateTable(createFieldIds.contains(field.getId()))).toList();
    }

    @Override
    public Set<Integer> getModelCreateTableFieldIds(Integer id) {
        Set<Integer> fieldIds = new HashSet<>();
        for (DataStorage dataStorage : dataStorageMapper.selectByDataModelId(id)) {
            if (ObjectUtils.isNotEmpty(dataStorage.getFieldIds())) {
                fieldIds.addAll(dataStorage.getFieldIds());
            }
        }
        return fieldIds;
    }

    @Override
    public void createDataStorages(DataModel dataModel, Collection<Integer> storagePointIds) {
        for (Integer storagePointId : storagePointIds) {
            DataStorage storage = new DataStorage();
            storage.setDataModelId(dataModel.getId());
            storage.setEnName(dataModel.getEnName());
            storage.setZhName(dataModel.getZhName());
            storage.setConnectionId(storagePointId);
            storage.setCreateTableStatus(CreateTableStatus.NOT);
            dataStorageMapper.insert(storage);
        }
    }

    @Override
    public void createDefaultMonitorConfig(Integer dataModelId) {
        createDefaultLagMonitorConfig(dataModelId);
        createDefaultFluctuationMonitorConfig(dataModelId);
        createDefaultCutoffMonitorConfig(dataModelId);
    }

    private void createDefaultLagMonitorConfig(Integer dataModelId) {
        DataModelMonitorConfig monitorConfig = new DataModelMonitorConfig();
        monitorConfig.setFieldsByDataModelId(dataModelId, com.trs.moye.base.common.enums.TimeUnit.MINUTE,
            MonitorConfigType.LAG, 10);
        monitorConfig.setThreshold(5000);
        monitorConfig.setXxlJobId(dataModelConfigService.createMonitorConfigXxlJob(monitorConfig));
        dataModelMonitorConfigMapper.insert(monitorConfig);
    }

    private void createDefaultFluctuationMonitorConfig(Integer dataModelId) {
        DataModelMonitorConfig monitorConfig = new DataModelMonitorConfig();
        monitorConfig.setFieldsByDataModelId(dataModelId, com.trs.moye.base.common.enums.TimeUnit.DAY,
            MonitorConfigType.FLUCTUATION, 1);
        monitorConfig.setThreshold(10);
        monitorConfig.setXxlJobId(dataModelConfigService.createMonitorConfigXxlJob(monitorConfig));
        dataModelMonitorConfigMapper.insert(monitorConfig);
    }

    private void createDefaultCutoffMonitorConfig(Integer dataModelId) {
        DataModelMonitorConfig monitorConfig = new DataModelMonitorConfig();
        monitorConfig.setFieldsByDataModelId(dataModelId, com.trs.moye.base.common.enums.TimeUnit.MINUTE,
            MonitorConfigType.CUTOFF, 10);
        monitorConfig.setXxlJobId(dataModelConfigService.createMonitorConfigXxlJob(monitorConfig));
        dataModelMonitorConfigMapper.insert(monitorConfig);
    }


    @Override
    public void createDefaultExecuteConfig(Integer dataModelId, DataConnection dataConnection,
        List<OdsFieldRequest> modelFields) {
        DataModelExecuteConfig executeConfig = new DataModelExecuteConfig();
        executeConfig.setDataModelId(dataModelId);
        if (dataConnection.isNeedDefaultIncrement()) {
            //默认给个增量信息
            executeConfig.setIncrementInfo(new IncrementInfo());
        } else if (ObjectUtils.isNotEmpty(modelFields)) {
            modelFields.stream()
                .filter(OdsFieldRequest::isIncrement)
                .findFirst()
                .map(field -> {
                    IncrementInfo incrementInfo = new IncrementInfo();
                    incrementInfo.setFieldName(field.getEnName());
                    incrementInfo.setFieldType(field.getType());
                    return incrementInfo;
                })
                .ifPresent(executeConfig::setIncrementInfo);
        }
        dataModelExecuteConfigMapper.insert(executeConfig);
    }

    @Override
    public boolean moveMetaDataToCategory(Set<Integer> dataModelIds, Integer businessId) {
        // 校验 业务分类、元数据 的存在性
        BusinessCategory businessCategory = businessCategoryMapper.selectById(businessId);
        if (Objects.isNull(businessCategory)) {
            throw new BizException(String.format("未知业务分类[businessId:%s]", businessId));
        }
        for (Integer metaDataId : dataModelIds) {
            DataModel dataModel = dataModelMapper.selectById(metaDataId);
            if (Objects.isNull(dataModel)) {
                throw new BizException(String.format("未知元数据[metaDataId:%s]", metaDataId));
            }
        }
        return dataModelMapper.updateBusinessCategoryIdByIds(businessId, dataModelIds);
    }

    @Override
    public List<BatchDeleteResponse> deleteDataModel(List<Integer> ids) {
        // 查询相应元数据列表
        List<DataModel> dataModels = dataModelMapper.selectByIds(ids);
        // 异步化 删除操作
        List<Future<?>> futures = new ArrayList<>(dataModels.size());
        for (DataModel dataModel : dataModels) {
            futures.add(batchDeleteExecutorService.submit(() -> singleDeleteDataModel(dataModel)));
        }

        // 收集结果
        List<BatchDeleteResponse> batchDeleteResultList = new ArrayList<>(dataModels.size());
        Iterator<DataModel> dataModelIterator = dataModels.iterator();
        // 遍历 futures 收集结果，futures 与 dataSourceTemplateIterator 顺序相同
        for (Future<?> future : futures) {
            // future 对应的 元数据
            DataModel dataModel = dataModelIterator.next();
            BatchDeleteResponse batchDeleteResult = new BatchDeleteResponse(dataModel.getId(), dataModel.getEnName(),
                dataModel.getZhName());
            batchDeleteResult.setIsSuccess(Boolean.FALSE);
            batchDeleteResult.setErrorMsg("");
            // future 对应的执行情况
            try {
                future.get();
                batchDeleteResult.setIsSuccess(Boolean.TRUE);
            } catch (InterruptedException e) {
                batchDeleteResult.setErrorMsg(e.getMessage());
                Thread.currentThread().interrupt();
            } catch (ExecutionException e) {
                batchDeleteResult.setErrorMsg(e.getCause().getMessage());
            } finally {
                batchDeleteResultList.add(batchDeleteResult);
            }
        }
        return batchDeleteResultList;
    }

    @Override
    public void singleDeleteDataModel(DataModel dataModel) {
        // 数据建模 数仓分层 判断
        ModelLayer layer = dataModel.getLayer();
        if (ModelLayer.ODS.equals(layer)) {
            // 删除贴源库
            deleteOds(dataModel);
        } else {
            // 删除要素库\专题库\主题库
            deleteDwd(dataModel);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteModel(Integer dataModelId) {
        DataModel dataModel = dataModelMapper.selectById(dataModelId);
        return deleteDwd(dataModel);
    }

    private Boolean deleteDwd(DataModel dataModel) {
        Integer dataModelId = dataModel.getId();
        //校验
        checkDeletable(dataModel);
        //没有问题执行删除
        //删除调度信息
        deleteScheduleConfig(dataModelId);
        transactionTemplate.execute(status -> {
            try {
                //删除数据来源
                dataSourceConfigMapper.deleteByDataModelId(dataModelId);
                //删除调度策略关
                dataModelScheduleConfigMapper.deleteByDataModelId(dataModelId);
                //删除建模
                dataModelMapper.deleteById(dataModelId);
                //删除存储信息
                dataStorageMapper.deleteByModelId(dataModelId);
                //删除执行配置
                dataModelExecuteConfigMapper.deleteByDataModelId(dataModelId);
                //删除建模字段
                dataModelFieldMapper.deleteByDataModelId(dataModelId);
                //删除批处理相关表内容
                batchArrangementService.deleteBatchArrange(dataModelId);
                // 删除聚合表编排（主题库专题库编排）
                aggregationTableArrangementMapper.deleteByModelId(dataModelId);
                //删除流处理相关内容
                operatorPipelineService.deleteOperatorPipeline(dataModelId);
                // 删除视图信息
                dataModelViewInfoMapper.deleteByDataModelId(dataModelId);
                //删除消息推送
                noticeSendConfInsideMapper.deleteByDataModelId(dataModelId);
                //指标库删除配置
                indicatorConfigMapper.deleteByDataModelId(dataModelId);
                //删除指标字段
                indicatorFieldMapper.deleteByDataModelId(dataModelId);
            } catch (Exception e) {
                status.setRollbackOnly();
                throw new BizException(e);
            }
            return Boolean.TRUE;
        });

        //通知mcp服务更新
        try {
            mcpServiceFeign.updateResources(dataModelId, false);
        } catch (Exception e) {
            log.error("更新MCP服务资源失败, id: {}", dataModelId, e);
        }

        return Boolean.TRUE;
    }

    private void checkDeletable(DataModel dataModel) {
        if (ModelExecuteStatus.START.equals(dataModel.getExecuteStatus()) || ModelExecuteStatus.PAUSE.equals(
            dataModel.getExecuteStatus())) {
            throw new BizException("启用/暂停中的数据建模无法删除！请先停止任务后重试！");
        }

        UsageInfoResponse odsUsageInfo = getUsageInfoResponse(dataModel);
        if (odsUsageInfo.isUse()) {
            throw new BizException("%s\n不允许删除", odsUsageInfo.getMessage());
        }
    }

    @Override
    public UsageInfoResponse getModelUsageInfo(Integer id) {
        DataModel dataModel = dataModelMapper.selectById(id);
        return getUsageInfoResponse(dataModel);
    }

    private UsageInfoResponse getUsageInfoResponse(DataModel dataModel) {
        AssertUtils.notEmpty(dataModel, MSG_DATA_MODEL_NOT_EXIST, dataModel.getId());
        UsageInfoResponse response = new UsageInfoResponse();
        response.setDetail(new ArrayList<>());
        List<UsingObjects> usageDetailList = new ArrayList<>();
        // 使用当前数据建模的数据建模
        List<DataModel> usedDataModelList = dataModelMapper.selectByDataModelSourceId(dataModel.getId());
        if (ObjectUtils.isNotEmpty(usedDataModelList)) {
            UsingObjects usingObjects = new UsingObjects();
            usingObjects.setType(ModuleEnum.DATA_MODELING);
            usingObjects.setObjects(
                usedDataModelList.stream().map(model -> new KeyValueResponse(model.getId(), model.getZhName()))
                    .toList());
            usageDetailList.add(usingObjects);
        }
        // 使用当前数据建模的数据服务
        List<DataStorage> dataStorages = dataStorageMapper.selectByDataModelId(dataModel.getId());
        List<Integer> storageIds = dataStorages.stream().map(DataStorage::getId).toList();
        UsingObjects storageUsageInfo = null;
        if (!storageIds.isEmpty()) {
            storageUsageInfo = getStorageUsageDataServiceInfo(storageIds);
        }
        if (ObjectUtils.isNotEmpty(storageUsageInfo)) {
            usageDetailList.add(storageUsageInfo);
        }

        //使用当前数据建模的数据分析
        List<VisualAnalysisIdName> visualAnalysisCharts = visualAnalysisSubjectMapper.selectByDataModelId(
            dataModel.getId());
        if (Objects.nonNull(visualAnalysisCharts) && !visualAnalysisCharts.isEmpty()) {
            UsingObjects usingObjects = new UsingObjects();
            usingObjects.setType(ModuleEnum.VISUAL_ANALYSIS);
            usingObjects.setObjects(visualAnalysisCharts.stream().map(
                visualAnalysisSubject -> new KeyValueResponse(visualAnalysisSubject.getSubjectId(),
                    "主题:" + visualAnalysisSubject.getSubjectName() + "," + "图表:"
                        + visualAnalysisSubject.getChartName())).toList());
            usageDetailList.add(usingObjects);
        } else {
            String titles = visualAnalysisSubjectMapper.selectPublishedByDataModelId(dataModel.getId());
            if (Objects.nonNull(titles) && !titles.isEmpty()) {
                UsingObjects usingObjects = new UsingObjects();
                usingObjects.setType(ModuleEnum.VISUAL_ANALYSIS);
                usingObjects.setObjects(List.of(new KeyValueResponse(dataModel.getId(), "预览发布图表:" + titles)));
                usageDetailList.add(usingObjects);
            }
        }
        return new UsageInfoResponse(usageDetailList, dataModel.getZhName(), ModuleEnum.DATA_MODELING);
    }

    /**
     * 获取存储使用的数据服务信息
     *
     * @param storageIds 存储id列表
     * @return 使用的数据服务信息
     */
    private UsingObjects getStorageUsageDataServiceInfo(List<Integer> storageIds) {
        if (Objects.isNull(storageIds) || ObjectUtils.isEmpty(storageIds)) {
            throw new BizException("通过存储ID获取数据服务失败！建模的存储点id不能为空！");
        }
        List<DataService> dataServices = dataServiceMapper.selectByStorageIds(storageIds);
        if (ObjectUtils.isEmpty(dataServices)) {
            return null;
        }
        UsingObjects usingObjects = new UsingObjects();
        usingObjects.setType(ModuleEnum.DATA_SERVICE);
        usingObjects.setObjects(
            dataServices.stream().map(dataService -> new KeyValueResponse(dataService.getId(), dataService.getName()))
                .toList());
        return usingObjects;
    }

    @Override
    public void deleteExecuteConfig(Integer id) {
        dataModelExecuteConfigMapper.deleteByDataModelId(id);
    }

    /**
     * 删除调度配置 删除XXL Job任务和数据库中的调度配置记录
     *
     * @param id 数据建模ID
     */
    private void deleteScheduleConfig(Integer id) {
        DataModelScheduleConfig scheduleConfig = dataModelScheduleConfigMapper.selectByDataModelId(id);
        if (ObjectUtils.isEmpty(scheduleConfig)) {
            log.warn("主键为【{}】数据建模未配置调度信息", id);
            return;
        }
        // 按道理xxlJobId非空，但是实时处理的xxl-job是后加的，数据库存在脏数据，先添加一个非空判断避免报错
        if (ObjectUtils.isNotEmpty(scheduleConfig.getXxlJobId())) {
            xxlJobManager.deleteJob(scheduleConfig.getXxlJobId());
        }
        dataModelScheduleConfigMapper.deleteByDataModelId(id);
    }


    private void deleteMonitorConfig(Integer id) {
        List<DataModelMonitorConfig> monitorConfigList = dataModelMonitorConfigMapper.dataModeInuseConfigList(id);
        dataModelMonitorConfigMapper.deleteByDataModelId(id);
        for (DataModelMonitorConfig monitorConfig : monitorConfigList) {
            xxlJobManager.deleteJob(monitorConfig.getXxlJobId());
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void deleteOds(Integer id) {
        DataModel model = dataModelMapper.selectById(id);
        deleteOds(model);
    }

    private void deleteOds(DataModel model) {
        Integer id = model.getId();
        AssertUtils.notEmpty(model, "主键为【%s】的%s不存在", id, ModelLayer.ODS.getLabel());
        if (model.getExecuteStatus() == ModelExecuteStatus.START) {
            throw new BizException("%s【%s】正在运行中，不允许删除", ModelLayer.ODS.getLabel(), model.getZhName());
        }

        UsageInfoResponse odsUsageInfo = getModelUsageInfo(id);
        if (odsUsageInfo.isUse()) {
            throw new BizException("%s\n不允许删除", odsUsageInfo.getMessage());
        }

        transactionTemplate.execute(status -> {
            try {
                dataSourceConfigMapper.deleteByDataModelId(model.getId());
                dataStorageMapper.deleteByDataModelId(model.getId());
                dataModelFieldMapper.deleteByDataModelId(id);
                deleteScheduleConfig(id);
                deleteExecuteConfig(id);
                deleteMonitorConfig(id);
                dataModelMapper.deleteById(id);
                dataModelViewInfoMapper.deleteByDataModelId(id);
                noticeSendConfInsideMapper.deleteByDataModelId(id);
            } catch (Exception e) {
                status.setRollbackOnly();
                throw new BizException(e);
            }
            return null;
        });

        //通知mcp服务更新
        try {
            mcpServiceFeign.updateResources(id, false);
        } catch (Exception e) {
            log.error("更新MCP服务资源失败, id: {}", id, e);
        }
    }

    @Override
    public List<MoyeFieldResponse> getOdsNeedSyncFields(Integer id) {
        DataModel dataModel = dataModelMapper.selectById(id);
        List<DataSourceConfig> dataSourceConfigs = dataModel.getDataSource();
        if (dataSourceConfigs == null || dataSourceConfigs.isEmpty()) {
            throw new BizException("数据来源为空，无法同步字段", id);
        }
        if (dataSourceConfigs.size() > 1) {
            throw new BizException("数据来源大于一个，无法同步字段", id);
        }
        DataSourceConfig dataSourceConfig = dataSourceConfigs.get(0);
        List<DataModelField> dataModelFields = dataModel.getFields();

        if (dataModel.getLayer().equals(ModelLayer.ODS)) {
            // 贴源表通过存储引擎获取数据来源的字段
            List<MoyeFieldResponse> fieldResponseList = getDataSourceFieldsByStorageEngine(dataSourceConfig);
            return filterNeedSyncFields(dataModelFields, fieldResponseList);
        } else {
            DataModel sourceModel = dataModelMapper.selectById(dataSourceConfig.getSourceModelId());
            if (sourceModel == null) {
                throw new BizException("数据来源模型不存在，无法同步字段", dataSourceConfig.getSourceModelId());
            }
            // 过滤掉已经存在的字段
            List<DataModelField> sourceFields = sourceModel.getFields();
            return sourceFields.stream()
                .filter(field ->
                    !dataModelFields.stream().map(DataModelField::getEnName).toList().contains(field.getEnName()))
                .map(MoyeFieldResponse::new)
                .toList();
        }

    }

    private List<MoyeFieldResponse> filterNeedSyncFields(List<DataModelField> dataModelFields,
        List<MoyeFieldResponse> columnResponseList) {
        // 过滤出需要添加的英文名不存在的字段,更新未被使用的已存在的自定义字段
        Set<String> enNameSet = dataModelFields.stream().map(AbstractField::getEnName).collect(Collectors.toSet());
        return columnResponseList.stream().filter(field -> enNameSet.add(field.getEnName())).toList();
    }

    private void updateFields(Integer dataModelId, CreateModeEnum createMode, List<DataStorage> dataStorages,
        List<MoyeFieldResponse> columnResponseList, List<DataModelField> dataModelFields) {
        if (CreateModeEnum.REVERSE_MODELING.equals(createMode)) {
            // 处理逆向建模的逻辑
            handleReverseModeling(dataModelId, dataStorages, columnResponseList, dataModelFields);
        } else {
            // 处理非逆向建模的逻辑
            handleNonReverseModeling(dataStorages, columnResponseList, dataModelFields);
        }
    }


    private void handleReverseModeling(Integer dataModelId, List<DataStorage> dataStorages,
        List<MoyeFieldResponse> columnResponseList, List<DataModelField> dataModelFields) {
        // 更新字段的删除或修改
        for (DataModelField field : dataModelFields) {
            // 逆向建模不需要判断是否使用，直接更新或删除
            updateOrDeleteField(columnResponseList, field, false, true);
        }
        // 处理存储点的字段更新
        List<Integer> fieldIds = dataModelFieldMapper.listByDataModelId(dataModelId).stream().map(DataModelField::getId)
            .toList();

        for (DataStorage dataStorage : dataStorages) {
            dataStorage.setFieldIds(fieldIds);
            dataStorageMapper.updateById(dataStorage);
        }
    }

    private void handleNonReverseModeling(List<DataStorage> dataStorages, List<MoyeFieldResponse> columnResponseList,
        List<DataModelField> dataModelFields) {
        // 更新数据建模字段
        for (DataModelField field : dataModelFields) {
            boolean isUsed = isFieldUsedInDataStorages(dataStorages, field);
            // 更新未被使用的已存在的自定义字段
            updateOrDeleteField(columnResponseList, field, isUsed, false);
        }
    }

    /**
     * 判断字段是否在数据存储中被使用
     *
     * @param dataStorages 数据存储
     * @param field        田
     * @return boolean
     * <AUTHOR>
     * @since 2025/01/07 18:05:05
     */
    private boolean isFieldUsedInDataStorages(List<DataStorage> dataStorages, DataModelField field) {
        if (ObjectUtils.isEmpty(dataStorages)) {
            return false;
        }
        return dataStorages.stream().anyMatch(
            storage -> ObjectUtils.isNotEmpty(storage.getFieldIds()) && storage.getFieldIds().contains(field.getId()));
    }


    private void updateOrDeleteField(List<MoyeFieldResponse> columnResponseList, DataModelField field, boolean isUsed,
        boolean isReverseModeling) {
        // 如果字段已被使用，则不进行任何更新或删除
        if (isUsed) {
            return;
        }
        boolean isExist = updateFieldIfExists(columnResponseList, field);
        // 如果字段未存在并且是逆向建模或非标准字段，则删除
        if (!isExist && (isReverseModeling || !field.isUseStandard())) {
            dataModelFieldMapper.deleteById(field.getId());
        }
    }

    /**
     * 如果字段在 columnResponseList 中存在且不是数据标准字段，则更新字段
     *
     * @param columnResponseList 列响应列表
     * @param field              田
     * @return boolean
     * <AUTHOR>
     * @since 2025/01/07 18:04:42
     */
    private boolean updateFieldIfExists(List<MoyeFieldResponse> columnResponseList, DataModelField field) {
        for (MoyeFieldResponse columnResponse : columnResponseList) {
            if (columnResponse.getEnName().equals(field.getEnName())) {
                // 如果字段不是数据标准字段，则更新字段信息
                if (!field.isUseStandard()) {
                    field.setZhName(columnResponse.getZhName());
                    field.setType(columnResponse.getType());
                    field.setTypeName(columnResponse.getTypeName());
                    dataModelFieldMapper.updateById(field);
                }
                return true;
            }
        }
        return false;
    }


    @Override
    public void syncOdsFields(Integer id) {
        DataModel dataModel = dataModelMapper.selectById(id);
        AssertUtils.notEmpty(dataModel, MSG_DATA_MODEL_NOT_EXIST, id);
        DataSourceConfig dataSource = getOneDataSource(id);
        // 通过存储引擎获取数据来源的字段
        List<MoyeFieldResponse> fieldResponseList = getDataSourceFieldsByStorageEngine(dataSource);
        updateDataModelFields(id, fieldResponseList, dataModel, dataModel.getCreateMode());
    }

    @Override
    public void updateDataModelFields(Integer id, List<MoyeFieldResponse> fieldResponseList, DataModel dataModel,
        CreateModeEnum createMode) {
        if (ObjectUtils.isEmpty(fieldResponseList)) {
            log.warn("{}【{}】的来源未获取到字段信息", dataModel.getLayer().getLabel(), dataModel.getZhName());
            return;
        }
        // 过滤出需要添加的字段：英文名不存在的字段
        List<DataModelField> dataModelFields = dataModelFieldMapper.listByDataModelId(id);
        List<MoyeFieldResponse> needSyncFields = filterNeedSyncFields(dataModelFields, fieldResponseList);
        if (ObjectUtils.isNotEmpty(needSyncFields)) {
            needSyncFields.forEach(column -> dataModelFieldMapper.insert(column.toDataModelField(id)));
        }
        updateFields(id, createMode, dataModel.getDataStorages(), fieldResponseList, dataModelFields);

        dataModel.setSyncField(true);
        dataModelMapper.updateById(dataModel);
    }

    private List<MoyeFieldResponse> getDataSourceFieldsByStorageEngine(DataSourceConfig dataSource) {
        DataSourceSettings settings = dataSource.getSettings();
        FileColumnRequest fileColumnRequest = new FileColumnRequest();
        try {
            if (settings instanceof FtpDataSourceSettings) {
                fileColumnRequest = new FileColumnRequest((FtpDataSourceSettings) settings);
            }
        } catch (Exception e) {
            log.error("数据来源【{}】的设置信息转换为FileColumnRequest失败", dataSource.getEnName(), e);
        }

        return storageEngineService.getMoyeFieldsByStorageEngine(dataSource.getConnection(), dataSource.getEnName(),
            fileColumnRequest, dataSource.getDataModelId());
    }

    private List<MoyeFieldResponse> getDataSourceFieldsByStorageEngine(String storageName,
        DataConnection dataConnection) {
        return storageEngineService.getMoyeFieldsByStorageEngine(dataConnection, storageName, null, null);
    }

    @Override
    public List<CreateTableResponse> createTable(Integer dataModelId, CreateTableRequests createTableRequests) {
        DataModel dataModel = dataModelMapper.selectById(dataModelId);
        return createTableRequests.getTables().stream()
            .map(table -> createTable(dataModel, table, dataStorageMapper, storageEngineService)).toList();
    }

    /**
     * 建表
     *
     * @param dataModel            建模
     * @param table                建表信息
     * @param dataStorageMapper    dataStorageMapper
     * @param storageEngineService storageEngineService
     * @return 返回结果
     */
    public static CreateTableResponse createTable(DataModel dataModel, CreateTableRequest table,
        DataStorageMapper dataStorageMapper, StorageEngineService storageEngineService) {
        DataStorageSettings settings = table.getSettings();
        DataStorage storage = dataStorageMapper.selectById(table.getDataStorageId());
        StorageEngineResponse response = storageEngineService.createTable(table.getConnectionId(), dataModel.getId(),
            table.getDataStorageId(), settings);
        if (response.isSuccess()) {
            storage.setCreateTableStatus(CreateTableStatus.SUCCESS);
            log.info("建表字段id为：{}", dataModel.getFieldIds());
            storage.setFieldIds(dataModel.getFieldIds());
            storage.setSettings(settings);
        } else {
            storage.setCreateTableStatus(CreateTableStatus.FAIL);
        }
        dataStorageMapper.updateById(storage);
        return new CreateTableResponse(storage.getId(), response);
    }

    @Override
    public Boolean immediateExecute(ImmediateExecuteRequest request, Integer id) {
        //参数转换
        SubmitJobRequest param = SubmitJobRequest.from(request.getExecuteParams(), id);
        RestfulResponse restfulResponse;
        restfulResponse = storageEngineService.submitJob(param);
        if (restfulResponse.getCode() == 200) {
            log.info("贴源库【{}】立即执行成功", id);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public List<DataModelSourceDetailResponse> getDataModelSourceDetail(Integer id) {
        DataModel dataModel = dataModelMapper.selectById(id);
        AssertUtils.notEmpty(dataModel, MSG_DATA_MODEL_NOT_EXIST, id);
        AssertUtils.notEquals(dataModel.getLayer(), ModelLayer.ODS, "不支持获取【%s】建模的数据来源");
        List<DataSourceConfig> dataSourceConfigs = dataModel.getDataSource();
        DataModelScheduleConfig scheduleConfig = dataModelScheduleConfigMapper.selectByDataModelId(id);
        return dataSourceConfigs.stream().map(dataSourceConfig -> {
            DataModelSourceDetailResponse dataModelSourceDetailResponse = new DataModelSourceDetailResponse();
            dataModelSourceDetailResponse.setId(dataSourceConfig.getId());
            dataModelSourceDetailResponse.setZhName(dataSourceConfig.getZhName());
            dataModelSourceDetailResponse.setEnName(dataSourceConfig.getEnName());
            DataModel sourceDataModel = dataModelMapper.selectById(dataSourceConfig.getSourceModelId());
            //如果不是逆向建模需要有sourceDataModel
            if (!CreateModeEnum.REVERSE_MODELING.equals(dataModel.getCreateMode())) {
                AssertUtils.notEmpty(sourceDataModel, "数据来源建模【%s】不存在", dataSourceConfig.getSourceModelId());
                dataModelSourceDetailResponse.setSourceLayer(sourceDataModel.getLayer());
                if (sourceDataModel.getScheduleConfig() != null) {
                    dataModelSourceDetailResponse.setExecuteMode(sourceDataModel.getScheduleConfig().getExecuteMode());
                }
                dataModelSourceDetailResponse.setSourceStorageConnections(
                    sourceDataModel.getDataStorages().stream().map(
                        StoragePointConnection::new).toList());
            }
            DataConnection sourceConnection = dataSourceConfig.getConnection();
            if (scheduleConfig != null && scheduleConfig.getExecuteMode() == ExecuteModeEnum.REALTIME) {
                DataStorage storagePoint = streamProcessHelper.getStreamProcessSourceModelStoragePoint(dataModel);
                sourceConnection = storagePoint.getConnection();
                dataModelSourceDetailResponse.setZhName(sourceDataModel.getZhName());
                dataModelSourceDetailResponse.setEnName(sourceDataModel.getEnName());
            }
            if (sourceConnection != null) {
                dataModelSourceDetailResponse.setHost(sourceConnection.getConnectionParams().getHost());
                dataModelSourceDetailResponse.setPort(sourceConnection.getConnectionParams().getPort());
                dataModelSourceDetailResponse.setConnectionName(sourceConnection.getName());
                dataModelSourceDetailResponse.setConnectionType(sourceConnection.getConnectionType());
            }
            dataModelSourceDetailResponse.setSourceModelId(dataSourceConfig.getSourceModelId());
            return dataModelSourceDetailResponse;
        }).toList();
    }

    @Override
    public PageResponse<Map<String, Object>> getDataPreviewPageList(Integer dataModelId, Integer storageId,
        ConditionSearchParams request) {
        DataModel dataModel = dataModelMapper.selectById(dataModelId);
        AssertUtils.notEmpty(dataModel, MSG_DATA_MODEL_NOT_EXIST, dataModelId);
        request.setOperationType(ServiceConfigType.QUERY);
        StorageSearchResponse storageSearchResponse;
        if (CreateModeEnum.VIEW.equals(dataModel.getCreateMode())) {
            DataModelViewInfo viewInfo = dataModelViewInfoMapper.selectByDataModelId(dataModelId);
            storageSearchResponse = storageEngineService.conditionSqlQuery(viewInfo.getConnectionId(),
                viewInfo.getSql(), request);
        } else {
            DataStorage storage = dataModel.getDataStorage(storageId);
            storageSearchResponse = storageEngineService.conditionQuery(storage.getConnectionId(), storage.getEnName(),
                request);
        }

        return PageResponse.of(storageSearchResponse.getItems(), request.getPageParams().getPageNum(),
            storageSearchResponse.getTotal(), request.getPageParams().getPageSize());
    }

    @Override
    public Integer getErrorCount(Integer id) {
        DataModel dataModel = dataModelMapper.selectById(id);
        DataModelScheduleConfig scheduleConfig = dataModelScheduleConfigMapper.selectByDataModelId(id);
        return Objects.isNull(scheduleConfig) ? 0
            : dataModelExecuteStatusService.getUnreadErrorCountFromDb(dataModel.getId(), dataModel.getLayer(),
                scheduleConfig.getExecuteMode());
    }

    @Override
    public Boolean readErrorMessages(String id, ReadErrorMessagesRequest request) {
        ModelLayer layer = request.getModelLayer();
        if (ModelLayer.ODS.equals(layer)) {
            //贴源库
            return storageTaskMapper.updateReadErrorMessages(id);
        } else {
            if (request.getProcessType() == null) {
                throw new BizException("批处理必须传递processType参数");
            }
            if (ProcessType.STREAM.equals(request.getProcessType())) {
                //流处理
                return processMapper.updateReadErrorMessages(id);
            } else {
                //批处理
                return batchTaskRecordMapper.updateReadErrorMessages(id);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DataModel reverseModeling(Integer businessCategoryId, ModelLayer layer, Integer connectionId,
        ConnectionType connectionType, ReverseModelDTO reverseModelDTO) throws BizException {
        // 创建数据建模
        DataModel dataModel = createReverseModel(businessCategoryId, layer, reverseModelDTO, connectionId);
        // 创建数据源
        createReverseDataSource(connectionId, dataModel.getId(), connectionType, reverseModelDTO);
        // 同步字段
        syncOdsFields(dataModel.getId());
        // 创建数据存储
        createExistDataStorage(dataModel, dataModel.getEnName(), connectionId);
        return dataModel;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void reverseModelingByModelId(Integer dataModelId) throws BizException {
        // 根据 元数据id 查询 元数据、存储点信息
        DataModel dataModel = dataModelMapper.selectById(dataModelId);
        log.info("要素库[enName:{}, zhName:{}]开始逆向建模", dataModel.getEnName(), dataModel.getZhName());
        List<DataStorage> dbsInfo = dataModel.getDataStorages();
        if (Objects.isNull(dbsInfo) || dbsInfo.isEmpty()) {
            throw new BizException(
                String.format("%s[enName:%s, zhName:%s]逆向建模失败，要求存储点数量[>=1]，实际存储点数量[%s]",
                    dataModel.getLayer().getLabel(), dataModel.getEnName(), dataModel.getZhName(), dbsInfo.size()));
        }
        // 只取第一个存储点进行字段信息读取
        DataStorage dataStorage = dbsInfo.get(0);
        List<MoyeFieldResponse> fields = getDataSourceFieldsByStorageEngine(dataStorage.getEnName(),
            dataStorage.getConnection());
        // 更新字段信息
        updateDataModelFields(dataModelId, fields, dataModel, CreateModeEnum.REVERSE_MODELING);
        List<Integer> fieldIds = dataModelFieldMapper.listByDataModelId(dataModelId).stream().map(DataModelField::getId)
            .toList();
        dataStorage.setFieldIds(fieldIds);
        dataStorage.setCreateTableStatus(CreateTableStatus.SUCCESS);
        dataStorageMapper.updateById(dataStorage);
    }


    private DataModel createReverseModel(Integer businessCategoryId, ModelLayer layer,
        ReverseModelDTO reverseModelDTO, Integer connectionId) {
        DataModel model = new DataModel();
        DataModel dbModel = dataModelMapper.getByEnNameAndConnectionId(reverseModelDTO.getModelEnName(), connectionId);
        AssertUtils.empty(dbModel, "英文名为【%s】的%s已经存在", reverseModelDTO.getModelEnName(),
            dbModel == null ? "" : dbModel.getLayer().getLabel());
        model.setEnName(reverseModelDTO.getModelEnName());
        model.setZhName(reverseModelDTO.getModelZhName());
        model.setBusinessCategoryId(businessCategoryId);
        model.setCreateMode(CreateModeEnum.REVERSE_MODELING);
        model.setLayer(layer);
        model.setExecuteStatus(ModelExecuteStatus.STOP);
        model.setIsArranged(true);
        dataModelMapper.insert(model);
        return model;
    }

    private void createReverseDataSource(Integer dataSourceConnectionId, Integer dataModelId,
        ConnectionType connectionType, ReverseModelDTO reverseModelDTO) {
        DataSourceConfig dataSource = new DataSourceConfig();
        dataSource.setDataModelId(dataModelId);
        dataSource.setConnectionId(dataSourceConnectionId);
        dataSource.setEnName(reverseModelDTO.getModelEnName());
        dataSource.setZhName(reverseModelDTO.getModelZhName());
        DataSourceSettings dataSourceSettings = new DefaultDataSourceSettings();
        dataSourceSettings.setConnectionType(connectionType);
        dataSource.setSettings(dataSourceSettings);
        dataSourceConfigMapper.insert(dataSource);
    }

    @Override
    public Integer createExistDataStorage(DataModel dataModel, String tableName, Integer connectionId) {
        DataConnection connection = dataConnectionMapper.selectById(connectionId);
        AssertUtils.notEmpty(connection, "数据连接不存在");
        return createExistDataStorage(dataModel, connection, new StorageTable(tableName, null));
    }

    @Override
    public Integer createExistDataStorage(DataModel dataModel, DataConnection connection, StorageTable table) {
        DataStorage storage = new DataStorage();
        storage.setDataModelId(dataModel.getId());
        storage.setEnName(table.getTableName());
        storage.setZhName(table.getTableName());
        storage.setConnectionId(connection.getId());
        DataStorageSettings dataSourceSettings = new DefaultDataStorageSettings(connection.getConnectionType());
        storage.setSettings(dataSourceSettings);
        storage.setSaveMode(table.getSaveMode());
        List<Integer> fieldList = dataModelFieldMapper.selectByDataModelId(dataModel.getId()).stream()
            .map(DataModelField::getId).toList();
        storage.setFieldIds(fieldList);
        storage.setCreateTableStatus(CreateTableStatus.SUCCESS);
        dataStorageMapper.insert(storage);
        return storage.getId();
    }

    @Override
    public void exportFields(Integer id, FieldExportRequest request, HttpServletResponse response) {
        DataModel dataModel = dataModelMapper.selectById(id);
        AssertUtils.notEmpty(dataModel, MSG_DATA_MODEL_NOT_EXIST, id);
        List<Integer> fieldIds = request.getFieldIds();
        List<DataModelField> fields;
        if (ObjectUtils.isEmpty(fieldIds)) {
            fields = dataModelFieldMapper.selectByDataModelId(id);
        } else {
            fields = dataModelFieldMapper.selectByIds(fieldIds);
        }
        if (ObjectUtils.isEmpty(fields)) {
            throw new BizException("要导出的【%s】建模的字段不存在", dataModel.getZhName());
        }
        ExportStrategy exportStrategy = exportStrategyContext.getExportStrategy(request.getFileType());
        AssertUtils.notEmpty(exportStrategy, "不支持导出【%s】格式", request.getFileType());
        exportStrategy.exportModelFields(response, dataModel.getZhName(), fields);
    }

    @Override
    public void addDataModelFields(List<DataModelField> dataModelFields, DataModel dataModel) {
        // 校验数据建模是否有重复字段
        List<String> fieldNameList = dataModelFields.stream().map(DataModelField::getEnName).toList();
        List<DataModelField> duplicateDataModelFields = dataModelFieldMapper.listByEnNameCollection(fieldNameList,
            dataModel.getId());
        if (ObjectUtils.isNotEmpty(duplicateDataModelFields)) {
            throw new BizException("[%s]数据建模已存在字段[%s]", dataModel.getEnName(),
                duplicateDataModelFields.stream().map(DataModelField::getEnName).collect(Collectors.joining(",")));
        }
        // 新增数据建模字段
        dataModelFields.forEach(dataModelField -> dataModelField.setDataModelId(dataModel.getId()));
        dataModelFieldMapper.insert(dataModelFields);
    }

    /**
     * 批量指定数据建模删除数据建模字段
     *
     * @param fieldsList   待删除的字段列表, 根据{@link DataModelField#getEnName()}, {@link DataModelField#getType()}删除
     * @param dataModelIds 数据建模id列表
     */
    @Override
    public void deleteDataModelFields(List<DataModelField> fieldsList, List<Integer> dataModelIds) {
        Map<FieldType, List<String>> type2EnNameList = fieldsList.stream().collect(
            Collectors.groupingBy(DataModelField::getType,
                Collectors.mapping(DataModelField::getEnName, Collectors.toList())));
        dataModelFieldMapper.deleteByEnNameAndTypeAndDataModelIds(type2EnNameList, dataModelIds);
    }

    /**
     * 修改数据建模字段名称
     *
     * @param fields     待修改的字段列表, 根据{@link ChangeFieldNameDto#getOldName()}, {@link ChangeFieldNameDto#getType()}修改
     * @param dataModels 数据建模列表
     */
    @Override
    public void changeDataModelsFieldsName(List<ChangeFieldNameDto> fields, List<DataModel> dataModels) {
        if (CollectionUtils.isEmpty(dataModels) || CollectionUtils.isEmpty(fields)) {
            throw new IllegalArgumentException("修改数据建模字段名称失败，数据建模id列表和字段列表不能为空");
        }
        List<String> newFieldNameList = fields.stream().map(ChangeFieldNameDto::getNewName).toList();
        dataModels.forEach(dataModel -> {
            // 校验新名称不在数据建模字段中
            List<DataModelField> duplicateDataModelFields = dataModelFieldMapper.listByEnNameCollection(
                newFieldNameList, dataModel.getId());
            if (ObjectUtils.isNotEmpty(duplicateDataModelFields)) {
                throw new BizException("[%s]数据建模已存在字段[%s]", dataModel.getEnName(),
                    duplicateDataModelFields.stream().map(DataModelField::getEnName).collect(Collectors.joining(",")));
            }
            // 批量更新字段名称
            dataModelFieldMapper.updateFieldsName(dataModel.getId(), fields);
        });
    }

    @Override
    public PageResponse<StorageTask> getScheduleRecordPageList(Integer dataModelId,
        ScheduleRecordRequest requestParams) {
        DataModel dataModel = dataModelMapper.selectById(dataModelId);
        List<Integer> storageIds = dataModel.getDataStorages().stream().map(DataStorage::getId)
            .toList();
        TimeRangeParams timeRangeParams = requestParams.getTimeRangeParams();
        PageParams pageParams = requestParams.getPageParams();
        Page<StorageTask> storageTaskPage = storageTaskMapper.getPage(dataModelId, timeRangeParams.getMinTime(),
            timeRangeParams.getMaxTime(), requestParams.getExecuteStatus(), requestParams.getSearchParams(),
            pageParams.toPage(), requestParams.getShowOnlyHasData(), storageIds);
        return PageResponse.of(storageTaskPage);
    }
}