package com.trs.ai.moye.data.model.request;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.trs.ai.moye.data.model.enums.deserializer.ModelLayerDeserializer;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.connection.enums.DataSourceCategory;
import com.trs.moye.base.data.model.enums.CreateTableStatus;
import com.trs.moye.base.data.model.enums.ModelExecuteStatus;
import java.util.Collections;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CategoryTreeRequest {

    /**
     * 启用状态
     */
    private ModelExecuteStatus enableStatus;
    /**
     * 建表状态
     */
    private CreateTableStatus createTableStatus;
    /**
     * 治理状态
     */
    private Boolean arrangeStatus;
    /**
     * 数据源小类
     */
    private ConnectionType connectionType;
    /**
     * 数据源大类
     */
    private DataSourceCategory dataSourceCategory;
    /**
     * 数据源id
     */
    private Integer connectionId;
    /**
     * 模型层级
     */
    @JsonDeserialize(using = ModelLayerDeserializer.class)
    private ModelLayer modelLayerType;
    /**
     * 是否有未读异常
     */
    private Boolean hasUnreadError;
    /**
     * 搜索栏输入
     */
    private String search;

    /**
     * 是否已发布到MCP
     */
    private Boolean isMcpPublished;



    /**
     * 根据dataSourceCategory动态获取对应的ConnectionType列表 用于MyBatis查询中的动态筛选
     *
     * @return ConnectionType列表，如果dataSourceCategory为null则返回null
     */
    public List<ConnectionType> getConnectionTypesByCategory() {
        if (dataSourceCategory == null) {
            return Collections.emptyList();
        }
        return ConnectionType.filterConnectionType(type -> type.getCategory() == dataSourceCategory);
    }
}
