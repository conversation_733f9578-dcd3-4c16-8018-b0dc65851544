package com.trs.ai.moye.data.model.service.strategy.impl;

import com.trs.ai.moye.data.model.response.CodeTemplateResponse;
import com.trs.ai.moye.data.model.service.strategy.CodeTemplateGenerator;
import com.trs.ai.moye.data.model.service.strategy.util.HiveTypeMapper;
import com.trs.ai.moye.llm.TranslateService;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.data.model.dao.DataModelFieldMapper;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.source.dao.DataSourceConfigMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * SQL代码模板生成器 - 生成建表和数据加工两个任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SqlTemplateGenerator implements CodeTemplateGenerator {

    // 常量定义
    private static final String CODE_TYPE = "SQL";
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    // 任务名称常量
    private static final String CREATE_TABLE_TASK_NAME = "建表任务";
    private static final String DATA_GOVERNANCE_TASK_NAME = "数据加工任务";

    // 表名格式常量 - 修改为不包含default前缀
    private static final String TABLE_NAME_FORMAT = "%s";

    // 系统字段常量
    private static final String FIELD_TRS_ID = "trs_id";
    private static final String FIELD_TRS_IN_TIME = "trs_in_time";
    private static final String FIELD_TRS_SOURCE_TIME = "trs_source_time";
    private static final String FIELD_TRS_SOURCE_FROM = "trs_source_from";
    private static final String FIELD_TRS_MOYE_INPUT_TIME = "trs_moye_input_time";
    private static final String FIELD_ID = "id";

    // 系统字段定义
    private static final String TRS_ID_DEFINITION = "    trs_id STRING COMMENT '主键',\n";
    private static final String TRS_IN_TIME_DEFINITION = "    trs_in_time TIMESTAMP COMMENT '数据入库时间',\n";
    private static final String TRS_SOURCE_TIME_DEFINITION = "    trs_source_time TIMESTAMP COMMENT '数据上游表入库时间',\n";
    private static final String TRS_SOURCE_FROM_DEFINITION = "    trs_source_from STRING COMMENT '数据来源表',\n";

    // SQL关键字和格式常量
    private static final String CREATE_TABLE_FORMAT = "CREATE TABLE IF NOT EXISTS `%s`(\n";
    private static final String INSERT_INTO_FORMAT = "INSERT INTO \n    %s\n";
    private static final String FROM_FORMAT = "FROM \n    %s AS t\n";
    private static final String EXISTS_SUBQUERY_FORMAT = "            %s AS m\n";
    private static final String MD5_SINGLE_KEY_FORMAT = "MD5(CONCAT_WS('',CAST(t.`%s` AS STRING)))";
    private static final String MD5_COMPOSITE_KEY_FORMAT = "MD5(CONCAT_WS('|',%s))";
    private static final String MD5_DEFAULT_KEY_FORMAT = "MD5(CONCAT_WS('',CAST(t.id AS STRING)))";
    private static final String CAST_FORMAT = "CAST(t.`%s` AS STRING)";
    private static final String FIELD_SELECT_FORMAT = "    `%s`";
    private static final String STORED_AS_ORC = ") STORED AS ORC;\n\n";

    // 分隔符常量
    private static final String COMMA_NEWLINE = ",\n";
    private static final String NEWLINE = "\n";
    private static final String PIPE_SEPARATOR = "|";
    private static final String COMMA_SEPARATOR = ",";

    // 注释常量
    private static final String COMMENT_SEPARATOR = "-- ================================================\n";
    private static final String CREATE_TABLE_COMMENT_TITLE = "-- 建表SQL模板\n";
    private static final String DATA_GOVERNANCE_COMMENT_TITLE = "-- 数据加工SQL模板\n";
    private static final String TARGET_TABLE_FORMAT = "-- 目标表: %s (%s)\n";
    private static final String SOURCE_TABLE_FORMAT = "-- 源表: %s (%s)\n";
    private static final String MODEL_DESCRIPTION_FORMAT = "-- 模型描述: %s\n";
    private static final String BUSINESS_FIELDS_COUNT_FORMAT = "-- 业务字段数量: %d\n";
    private static final String PRIMARY_KEYS_COUNT_FORMAT = "-- 主键字段数量: %d\n";
    private static final String FIELDS_STATISTICS_FORMAT = "-- 字段分类统计: 总计=%d, 业务字段=%d, 主键=%d, 分区字段=%d, 增量字段=%d\n";
    private static final String LLM_TRANSLATION_INFO_FORMAT = "-- 大模型翻译: %s, 翻译字段数量: %d\n";
    private static final String GENERATE_TIME_FORMAT = "-- 生成时间: %s\n";
    private static final String NO_DESCRIPTION = "无";

    // 大模型翻译状态常量
    private static final String LLM_TRANSLATION_SUCCESS = "成功";
    private static final String LLM_TRANSLATION_SKIPPED = "未调用(无需翻译)";
    private static final String LLM_TRANSLATION_FAILED = "失败(%s)";

    // SQL注释常量
    private static final String CREATE_TARGET_TABLE_COMMENT = "-- 创建目标表\n";
    private static final String CREATE_TABLE_COMPLETE_COMMENT = "-- 建表完成，请检查表结构是否正确";
    private static final String DATA_GOVERNANCE_COMMENT = "-- 数据治理：从源表插入数据到目标表\n";
    private static final String DATA_GOVERNANCE_COMPLETE_COMMENT = "-- 数据加工完成，请根据实际需求调整WHERE条件";
    private static final String DEDUPLICATION_COMMENT = "    -- 去重条件：避免重复插入相同数据\n";
    private static final String ADDITIONAL_FILTER_COMMENT = "    -- 可根据需要添加其他过滤条件\n";
    private static final String STATUS_FILTER_EXAMPLE = "    -- AND t.status = 'ACTIVE'\n";
    private static final String TIME_FILTER_EXAMPLE = "    -- AND t.create_time >= '2024-01-01';\n\n";

    // 系统字段SELECT常量
    private static final String TRS_ID_SELECT_FORMAT = "    %s as trs_id,\n";
    private static final String TRS_IN_TIME_SELECT = "    CURRENT_TIMESTAMP as trs_in_time,\n";
    private static final String TRS_SOURCE_TIME_SELECT = "    trs_moye_input_time as trs_source_time,\n";
    private static final String TRS_SOURCE_FROM_SELECT_FORMAT = "    '%s' as trs_source_from,\n";

    // WHERE子句常量
    private static final String WHERE_CLAUSE = "WHERE\n";
    private static final String NOT_EXISTS_CLAUSE = "    NOT EXISTS (\n";
    private static final String SELECT_ONE_CLAUSE = "        SELECT \n            1\n        FROM\n";
    private static final String WHERE_TRS_ID_CLAUSE_FORMAT = "            m.trs_id = %s\n";
    private static final String CLOSE_EXISTS_CLAUSE = "    )\n";

    // 日志消息常量
    private static final String LOG_START_GENERATE = "===== 开始生成SQL模板 =====";
    private static final String LOG_MODEL_INFO = "数据模型: ID={}, 中文名={}, 英文名={}";
    private static final String LOG_QUERY_DATASOURCE = "1. 查询数据源配置...";
    private static final String LOG_DATASOURCE_INFO = "数据源配置: source_model_id={}, 源表名={}";
    private static final String LOG_QUERY_SOURCE_MODEL = "2. 查询源模型信息...";
    private static final String LOG_SOURCE_MODEL_INFO = "源模型信息: ID={}, 英文名={}, 中文名={}";
    private static final String LOG_GET_FIELDS = "3. 获取字段信息...";
    private static final String LOG_FIELDS_COUNT = "成功获取字段信息，共 {} 个字段";
    private static final String LOG_FIELDS_STATISTICS = "字段分类统计: 总计={}, 业务字段={}, 主键={}, 分区字段={}, 增量字段={}";
    private static final String LOG_TRANSLATE_FIELDS = "4. 检查并翻译字段信息...";
    private static final String LOG_GENERATE_TEMPLATE = "5. 生成SQL模板...";
    private static final String LOG_GENERATE_SUCCESS = "===== SQL模板生成成功，生成{}个任务 =====";
    private static final String LOG_BIZ_EXCEPTION = "业务异常: {}";
    private static final String LOG_GENERATE_EXCEPTION = "SQL模板生成异常";

    // 异常消息常量
    private static final String NO_DATASOURCE_CONFIG_MSG = "数据模型 '%s'(ID:%d) 没有配置数据源信息。\n请在数据建模页面配置数据源后再生成SQL模板。";
    private static final String NO_SOURCE_MODEL_ID_MSG = "数据模型 '%s'(ID:%d) 的数据源配置中缺少源模型ID。";
    private static final String SOURCE_MODEL_NOT_EXISTS_MSG = "源模型(ID:%d)不存在，请检查数据源配置。";
    private static final String NO_FIELDS_MSG = "源模型 '%s'(ID:%d) 没有配置字段信息。";
    private static final String NO_BUSINESS_FIELDS_MSG = "源模型 '%s'(ID:%d) 没有可用的业务字段生成SQL模板。";
    private static final String GENERATE_FAILED_MSG = "SQL模板生成失败: ";

    // 排重方案相关常量
    private static final String DEDUPLICATION_APPROACHES_TITLE = "-- ================================================\n";
    private static final String DEDUPLICATION_APPROACHES_DESC = "-- 排重方案说明:\n-- 1. 简单排重(NOT EXISTS): 适用于小规模数据，或只需基于主键简单去重的场景\n-- 2. 高级排重(ROW_NUMBER): 适用于大规模数据，需要保留最新记录的场景，性能更好\n-- 默认启用方案1，如需切换方案，请注释/取消注释相应代码块\n";
    private static final String DEDUPLICATION_APPROACHES_END = "-- ================================================\n\n";

    private static final String SIMPLE_DEDUP_START = "-- 方案1：简单排重(NOT EXISTS) - 开始\n";
    private static final String SIMPLE_DEDUP_END = "-- 方案1：简单排重(NOT EXISTS) - 结束\n\n";

    private static final String ADVANCED_DEDUP_START = "-- 方案2：高级排重(ROW_NUMBER) - 开始（如需使用，请取消注释）\n";
    private static final String ADVANCED_DEDUP_COMMENT_START = "/*\n";
    private static final String ADVANCED_DEDUP_COMMENT_END = "*/\n";
    private static final String ADVANCED_DEDUP_END = "-- 方案2：高级排重(ROW_NUMBER) - 结束\n\n";

    private static final String INCREMENTAL_SYNC_COMMENT = "    -- 下面是增量同步时间条件，根据需要启用\n";
    private static final String LAST_SYNC_TIME_QUERY = "    JOIN (\n        SELECT\n            IF (\n                MAX(trs_source_time) IS NULL,\n                CAST('1970-01-01 00:00:00' AS TIMESTAMP),\n                (MAX(trs_source_time) - INTERVAL 1 SECOND)\n            ) AS last_sync_time\n        FROM\n            %s\n    ) last_sync ON t.trs_moye_input_time > last_sync.last_sync_time\n";

    private static final String ROW_NUMBER_FORMAT = "        ROW_NUMBER() OVER (PARTITION BY %s ORDER BY trs_moye_input_time DESC) AS rn\n";
    private static final String FILTER_DATA_WHERE = "    WHERE\n        -- 添加必要的非空条件\n";
    private static final String PK_NOT_NULL_FORMAT = "        %s IS NOT NULL AND %s != '' AND\n";
    private static final String WHERE_1_1 = "        1=1 -- 保留此行，便于添加其他条件\n";
    private static final String FILTERED_DATA_ALIAS = ") filtered_data\n";
    private static final String WHERE_RN_1 = "WHERE\n    -- 只保留每组中的最新记录\n    rn = 1\n";
    private static final String AND_NOT_EXISTS = "    -- 避免重复插入已存在的记录\n    AND NOT EXISTS (\n        SELECT\n            1\n        FROM\n            %s existing\n        WHERE\n            existing.trs_id = filtered_data.trs_id\n    )\n;\n";


    private final DataModelFieldMapper dataModelFieldMapper;
    private final DataModelMapper dataModelMapper;
    private final DataSourceConfigMapper dataSourceConfigMapper;
    private final TranslateService translateService;

    /**
     * 字段翻译结果类，包含翻译后的字段列表和翻译状态信息
     */
    private static class FieldTranslationResult {
        private List<DataModelField> fields;
        private String translationStatus;
        private int fieldsToTranslateCount;

        public List<DataModelField> getFields() {
            return fields;
        }

        public void setFields(List<DataModelField> fields) {
            this.fields = fields;
        }

        public String getTranslationStatus() {
            return translationStatus;
        }

        public void setTranslationStatus(String translationStatus) {
            this.translationStatus = translationStatus;
        }

        public int getFieldsToTranslateCount() {
            return fieldsToTranslateCount;
        }

        public void setFieldsToTranslateCount(int fieldsToTranslateCount) {
            this.fieldsToTranslateCount = fieldsToTranslateCount;
        }
    }

    @Override
    public String getSupportedCodeType() {
        return CODE_TYPE;
    }

    @Override
    public CodeTemplateResponse generate(DataModel dataModel) {
        Integer dataModelId = dataModel.getId();
        log.info(LOG_START_GENERATE);
        log.info(LOG_MODEL_INFO, dataModelId, dataModel.getZhName(), dataModel.getEnName());

        try {
            // 1. 查询数据源配置，获取 source_model_id
            log.info(LOG_QUERY_DATASOURCE);
            DataSourceConfig dataSourceConfig = dataSourceConfigMapper.selectOneByDataModelId(dataModelId);

            if (dataSourceConfig == null) {
                throw new BizException(String.format(NO_DATASOURCE_CONFIG_MSG,
                        dataModel.getZhName(), dataModelId));
            }

            Integer sourceModelId = dataSourceConfig.getSourceModelId();
            if (sourceModelId == null) {
                throw new BizException(String.format(NO_SOURCE_MODEL_ID_MSG,
                        dataModel.getZhName(), dataModelId));
            }

            log.info(LOG_DATASOURCE_INFO, sourceModelId, dataSourceConfig.getEnName());

            // 2. 查询源模型信息（用于获取源表信息）
            log.info(LOG_QUERY_SOURCE_MODEL);
            DataModel sourceModel = dataModelMapper.selectById(sourceModelId);

            if (sourceModel == null) {
                throw new BizException(String.format(SOURCE_MODEL_NOT_EXISTS_MSG, sourceModelId));
            }

            log.info(LOG_SOURCE_MODEL_INFO,
                    sourceModel.getId(), sourceModel.getEnName(), sourceModel.getZhName());

            // 3. 获取字段信息（从源模型的字段）
            log.info(LOG_GET_FIELDS);
            List<DataModelField> allFields = dataModelFieldMapper.listByDataModelId(sourceModelId);

            if (allFields == null || allFields.isEmpty()) {
                throw new BizException(String.format(NO_FIELDS_MSG,
                        sourceModel.getZhName(), sourceModelId));
            }

            log.info(LOG_FIELDS_COUNT, allFields.size());

            // 4. 检查并翻译字段中文名和描述
            log.info(LOG_TRANSLATE_FIELDS);
            FieldTranslationResult translationResult = ensureFieldTranslation(sourceModelId, allFields);
            allFields = translationResult.getFields();

            // 5. 过滤掉需要忽略的字段（内置字段和系统忽略字段）
            List<DataModelField> businessFields = allFields.stream()
                    .filter(field -> !HiveTypeMapper.shouldIgnoreField(field))
                    .toList();

            // 6. 查找主键字段
            List<DataModelField> primaryKeys = findPrimaryKeyFields(allFields);

            // 7. 查找分区字段
            List<DataModelField> partitionFields = allFields.stream()
                    .filter(DataModelField::isPartition)
                    .toList();

            // 8. 查找增量字段
            List<DataModelField> incrementFields = allFields.stream()
                    .filter(DataModelField::isIncrement)
                    .toList();

            log.info(LOG_FIELDS_STATISTICS,
                    allFields.size(), businessFields.size(), primaryKeys.size(),
                    partitionFields.size(), incrementFields.size());

            if (businessFields.isEmpty()) {
                throw new BizException(String.format(NO_BUSINESS_FIELDS_MSG,
                        sourceModel.getZhName(), sourceModelId));
            }

            // 9. 生成两个任务的代码模板
            log.info(LOG_GENERATE_TEMPLATE);

            // 任务1：建表SQL
            String createTableCode = generateCreateTableSQL(dataModel, sourceModel,
                    businessFields, partitionFields, allFields, primaryKeys, incrementFields, translationResult);
            CodeTemplateResponse.TaskTemplate createTableTask = new CodeTemplateResponse.TaskTemplate(
                    CREATE_TABLE_TASK_NAME, createTableCode);

            // 任务2：数据加工SQL
            String dataGovernanceCode = generateDataGovernanceSQL(dataModel, sourceModel,
                    businessFields, primaryKeys, allFields, partitionFields, incrementFields, translationResult);
            CodeTemplateResponse.TaskTemplate dataGovernanceTask = new CodeTemplateResponse.TaskTemplate(
                    DATA_GOVERNANCE_TASK_NAME, dataGovernanceCode);

            List<CodeTemplateResponse.TaskTemplate> tasks = List.of(createTableTask, dataGovernanceTask);

            log.info(LOG_GENERATE_SUCCESS, tasks.size());

            return new CodeTemplateResponse(tasks);

        } catch (BizException e) {
            log.error(LOG_BIZ_EXCEPTION, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error(LOG_GENERATE_EXCEPTION, e);
            throw new BizException(GENERATE_FAILED_MSG + e.getMessage());
        }
    }

    /**
     * 确保字段有正确的中文名和描述
     *
     * @param dataModelId 数据模型ID
     * @param fields 字段列表
     * @return 包含翻译后的字段列表和翻译信息的结果对象
     */
    private FieldTranslationResult ensureFieldTranslation(Integer dataModelId, List<DataModelField> fields) {
        // 创建结果对象
        FieldTranslationResult result = new FieldTranslationResult();
        result.setFields(fields);

        // 过滤出需要翻译的字段
        List<DataModelField> fieldsToTranslate = fields.stream()
                .filter(this::needTranslation)
                .collect(Collectors.toList());

        // 设置需要翻译的字段数量
        result.setFieldsToTranslateCount(fieldsToTranslate.size());

        // 如果没有需要翻译的字段，直接返回原字段列表
        if (fieldsToTranslate.isEmpty()) {
            log.info("所有字段已有合适的中文名和描述，无需调用大模型翻译");
            result.setTranslationStatus(LLM_TRANSLATION_SKIPPED);
            return result;
        }

        // 如果有需要翻译的字段，调用大模型进行翻译
        log.info("发现{}个字段需要完善中文名或描述，调用大模型进行翻译", fieldsToTranslate.size());

        try {
            List<DataModelField> translatedFields = translateService.translateFields(dataModelId, fieldsToTranslate);

            // 将翻译结果合并回原字段列表
            Map<String, DataModelField> translatedFieldMap = translatedFields.stream()
                    .collect(Collectors.toMap(DataModelField::getEnName, field -> field));

            List<DataModelField> mergedFields = fields.stream()
                    .map(field -> {
                        if (translatedFieldMap.containsKey(field.getEnName())) {
                            DataModelField translatedField = translatedFieldMap.get(field.getEnName());
                            field.setZhName(translatedField.getZhName());
                            field.setDescription(translatedField.getDescription());
                        }
                        return field;
                    })
                    .collect(Collectors.toList());

            result.setFields(mergedFields);
            result.setTranslationStatus(LLM_TRANSLATION_SUCCESS);
            return result;
        } catch (Exception e) {
            log.error("调用大模型翻译字段失败", e);
            result.setTranslationStatus(String.format(LLM_TRANSLATION_FAILED, e.getMessage()));
            return result;
        }
    }

    /**
     * 判断字段是否需要翻译
     *
     * @param field 字段
     * @return 是否需要翻译
     */
    private boolean needTranslation(DataModelField field) {
        // 检查中文名是否为空或不是中文
        boolean needZhNameTranslation = StringUtils.isBlank(field.getZhName())
                ||
                !isChineseText(field.getZhName());

        // 检查描述是否为空或太短
        boolean needDescriptionTranslation = StringUtils.isBlank(field.getDescription())
                ||
                field.getDescription().length() < 5;

        return needZhNameTranslation || needDescriptionTranslation;
    }

    /**
     * 判断文本是否包含中文
     *
     * @param text 文本
     * @return 是否包含中文
     */
    private boolean isChineseText(String text) {
        if (StringUtils.isBlank(text)) {
            return false;
        }

        // 使用正则表达式检查是否包含中文字符
        Pattern pattern = Pattern.compile("[\\u4e00-\\u9fa5]");
        Matcher matcher = pattern.matcher(text);
        return matcher.find();
    }

    /**
     * 查找主键字段
     *
     * @param allFields 所有字段列表
     * @return {@link List }<{@link DataModelField }>
     */
    private List<DataModelField> findPrimaryKeyFields(List<DataModelField> allFields) {
        return allFields.stream()
                .filter(DataModelField::isPrimaryKey)
                .collect(Collectors.toList());
    }

    /**
     * 生成建表SQL（单独任务）
     *
     * @param targetModel 目标数据模型
     * @param sourceModel 源数据模型
     * @param businessFields 业务字段列表
     * @param partitionFields 分区字段列表
     * @param allFields 所有字段列表
     * @param primaryKeys 主键字段列表
     * @param incrementFields 增量字段列表
     * @param translationResult 字段翻译结果
     * @return {@link String }
     */
    @SuppressWarnings("checkstyle:ParameterNumber")
    private String generateCreateTableSQL(DataModel targetModel, DataModel sourceModel,
            List<DataModelField> businessFields, List<DataModelField> partitionFields,
            List<DataModelField> allFields, List<DataModelField> primaryKeys,
            List<DataModelField> incrementFields, FieldTranslationResult translationResult) {

        StringBuilder sql = new StringBuilder();

        // 添加注释头
        sql.append(buildCreateTableCommentHeader(targetModel, sourceModel, businessFields,
                allFields, primaryKeys, partitionFields, incrementFields, translationResult));

        // 生成建表语句 - 不使用default前缀
        String tableName = targetModel.getEnName();

        sql.append(CREATE_TARGET_TABLE_COMMENT);
        sql.append(String.format(CREATE_TABLE_FORMAT, tableName));

        // 添加系统标准字段
        sql.append(TRS_ID_DEFINITION);
        sql.append(TRS_IN_TIME_DEFINITION);
        sql.append(TRS_SOURCE_TIME_DEFINITION);
        sql.append(TRS_SOURCE_FROM_DEFINITION);

        // 添加非分区业务字段
        List<DataModelField> nonPartitionFields = businessFields.stream()
                .filter(field -> !field.isPartition())
                .collect(Collectors.toList());

        for (int i = 0; i < nonPartitionFields.size(); i++) {
            DataModelField field = nonPartitionFields.get(i);
            sql.append(HiveTypeMapper.buildHiveFieldDefinition(field));
            if (i < nonPartitionFields.size() - 1 || !partitionFields.isEmpty()) {
                sql.append(COMMA_NEWLINE);
            } else {
                sql.append(NEWLINE);
            }
        }

        sql.append(STORED_AS_ORC);
        sql.append(CREATE_TABLE_COMPLETE_COMMENT);

        return sql.toString();
    }

    /**
     * 生成数据加工SQL（单独任务）
     *
     * @param targetModel 目标数据模型
     * @param sourceModel 源数据模型
     * @param businessFields 业务字段列表
     * @param primaryKeys 主键字段列表
     * @param allFields 所有字段列表
     * @param partitionFields 分区字段列表
     * @param incrementFields 增量字段列表
     * @param translationResult 字段翻译结果
     * @return {@link String }
     */

    private String generateDataGovernanceSQL(DataModel targetModel, DataModel sourceModel,
            List<DataModelField> businessFields, List<DataModelField> primaryKeys,
            List<DataModelField> allFields, List<DataModelField> partitionFields,
            List<DataModelField> incrementFields, FieldTranslationResult translationResult) {

        StringBuilder sql = new StringBuilder();

        // 添加注释头
        sql.append(buildDataGovernanceCommentHeader(targetModel, sourceModel, businessFields, primaryKeys,
                allFields, partitionFields, incrementFields, translationResult));

        // 使用不带default前缀的表名
        String targetTableName = targetModel.getEnName();
        String sourceTableName = sourceModel.getEnName();

        sql.append(DATA_GOVERNANCE_COMMENT);

        // 添加排重方案说明
        sql.append(COMMENT_SEPARATOR);
        sql.append("-- 排重方案说明:\n");
        sql.append("-- 1. 简单排重(NOT EXISTS): 适用于小规模数据，或只需基于主键简单去重的场景，能防止不同批次执行时插入相同ID的记录，但无法防止同一批次内源表中已有的重复记录被插入\n");
        sql.append("-- 2. 高级排重(ROW_NUMBER): 适用于大规模数据，需要保留最新记录的场景，性能更好，可以在源表层面就进行去重，确保即使源表中有重复记录，也只会选择一条插入目标表\n");
        sql.append("-- 默认启用方案1，如需切换方案，请注释/取消注释相应代码块\n");
        sql.append(DEDUPLICATION_APPROACHES_END);

        // 方案1：简单排重(NOT EXISTS)
        sql.append(SIMPLE_DEDUP_START);
        sql.append(String.format(INSERT_INTO_FORMAT, targetTableName));
        sql.append("SELECT \n");

        // 生成主键MD5
        String primaryKeyMd5 = generateprimaryKeyMd5(primaryKeys);
        sql.append(String.format(TRS_ID_SELECT_FORMAT, primaryKeyMd5));
        sql.append(TRS_IN_TIME_SELECT);
        sql.append(TRS_SOURCE_TIME_SELECT);
        sql.append(String.format(TRS_SOURCE_FROM_SELECT_FORMAT, sourceTableName));

        // 添加业务字段
        for (int i = 0; i < businessFields.size(); i++) {
            DataModelField field = businessFields.get(i);
            sql.append(String.format(FIELD_SELECT_FORMAT, field.getEnName()));
            if (i < businessFields.size() - 1) {
                sql.append(COMMA_NEWLINE);
            } else {
                sql.append(NEWLINE);
            }
        }

        sql.append(String.format(FROM_FORMAT, sourceTableName));
        sql.append(WHERE_CLAUSE);
        sql.append(DEDUPLICATION_COMMENT);
        sql.append(NOT_EXISTS_CLAUSE);
        sql.append(SELECT_ONE_CLAUSE);
        sql.append(String.format(EXISTS_SUBQUERY_FORMAT, targetTableName));
        sql.append("        WHERE \n");
        sql.append(String.format(WHERE_TRS_ID_CLAUSE_FORMAT, primaryKeyMd5));
        sql.append(CLOSE_EXISTS_CLAUSE);
        sql.append(ADDITIONAL_FILTER_COMMENT);
        sql.append(STATUS_FILTER_EXAMPLE);
        sql.append(TIME_FILTER_EXAMPLE);
        sql.append(SIMPLE_DEDUP_END);

        // 方案2：高级排重(ROW_NUMBER)
        sql.append(ADVANCED_DEDUP_START);
        sql.append("/*\n");
        sql.append(String.format(INSERT_INTO_FORMAT, targetTableName));
        sql.append("SELECT\n");
        sql.append("    trs_id,\n");
        sql.append("    trs_in_time,\n");
        sql.append("    trs_source_time,\n");
        sql.append("    trs_source_from,\n");

        // 添加业务字段
        for (int i = 0; i < businessFields.size(); i++) {
            DataModelField field = businessFields.get(i);
            sql.append(String.format("    %s", field.getEnName()));
            if (i < businessFields.size() - 1) {
                sql.append(COMMA_NEWLINE);
            } else {
                sql.append(NEWLINE);
            }
        }

        sql.append("FROM (\n");
        sql.append("    SELECT\n");
        sql.append(String.format("        %s AS trs_id,\n", primaryKeyMd5));
        sql.append("        CURRENT_TIMESTAMP AS trs_in_time,\n");
        sql.append("        trs_moye_input_time AS trs_source_time,\n");
        sql.append(String.format("        '%s' AS trs_source_from,\n", sourceTableName));

        // 添加业务字段
        for (DataModelField field : businessFields) {
            sql.append(String.format("        %s,\n", field.getEnName()));
        }

        // 确定用于排序的主键字段（如果有多个主键，取第一个）
        String partitionField = primaryKeys.isEmpty() ? "id" : primaryKeys.get(0).getEnName();

        // 添加行号
        sql.append(String.format(ROW_NUMBER_FORMAT, partitionField));
        sql.append(String.format("    FROM\n        %s AS t\n", sourceTableName));

        // 添加增量时间过滤（可选）
        sql.append("    JOIN (\n");
        sql.append("        SELECT\n");
        sql.append("            IF (\n");
        sql.append("                MAX(trs_source_time) IS NULL,\n");
        sql.append("                CAST('1970-01-01 00:00:00' AS TIMESTAMP),\n");
        sql.append("                (MAX(trs_source_time) - INTERVAL 1 SECOND)\n");
        sql.append("            ) AS last_sync_time\n");
        sql.append(String.format("        FROM\n            %s\n", targetTableName));
        sql.append("    ) last_sync ON t.trs_moye_input_time > last_sync.last_sync_time\n");

        // 添加业务过滤条件
        sql.append("    WHERE\n");
        sql.append("        -- 添加必要的非空条件\n");

        // 为主键字段添加非空条件
        if (!primaryKeys.isEmpty()) {
            for (DataModelField pk : primaryKeys) {
                sql.append(String.format(PK_NOT_NULL_FORMAT,
                        pk.getEnName(), pk.getEnName()));
            }
        }

        sql.append(WHERE_1_1);
        sql.append(FILTERED_DATA_ALIAS);
        sql.append(WHERE_CLAUSE);
        sql.append("    -- 只保留每组中的最新记录\n");
        sql.append("    rn = 1\n");
        sql.append("    -- 避免重复插入已存在的记录\n");
        sql.append("    AND NOT EXISTS (\n");
        sql.append("        SELECT\n");
        sql.append("            1\n");
        sql.append("        FROM\n");
        sql.append(String.format("            %s existing\n", targetTableName));
        sql.append("        WHERE\n");
        sql.append("            existing.trs_id = filtered_data.trs_id\n");
        sql.append(CLOSE_EXISTS_CLAUSE);
        sql.append(";\n");
        sql.append("*/\n");
        sql.append(ADVANCED_DEDUP_END);

        sql.append(DATA_GOVERNANCE_COMPLETE_COMMENT);

        return sql.toString();
    }

    /**
     * 构建建表任务的注释头
     *
     * @param targetModel 目标数据模型
     * @param sourceModel 源数据模型
     * @param businessFields 业务字段列表
     * @param allFields 所有字段列表
     * @param primaryKeys 主键字段列表
     * @param partitionFields 分区字段列表
     * @param incrementFields 增量字段列表
     * @param translationResult 字段翻译结果
     * @return {@link String }
     */
    private String buildCreateTableCommentHeader(DataModel targetModel, DataModel sourceModel,
            List<DataModelField> businessFields, List<DataModelField> allFields,
            List<DataModelField> primaryKeys, List<DataModelField> partitionFields,
            List<DataModelField> incrementFields, FieldTranslationResult translationResult) {
        StringBuilder header = new StringBuilder();
        header.append(COMMENT_SEPARATOR);
        header.append(CREATE_TABLE_COMMENT_TITLE);
        header.append(COMMENT_SEPARATOR);
        header.append(String.format(TARGET_TABLE_FORMAT, targetModel.getZhName(), targetModel.getEnName()));
        header.append(String.format(SOURCE_TABLE_FORMAT, sourceModel.getZhName(), sourceModel.getEnName()));
//        header.append(String.format(MODEL_DESCRIPTION_FORMAT,
//                StringUtils.defaultIfBlank(targetModel.getDescription(), NO_DESCRIPTION)));
        header.append(String.format(BUSINESS_FIELDS_COUNT_FORMAT, businessFields.size()));

        // 添加字段分类统计信息
        header.append(String.format(FIELDS_STATISTICS_FORMAT,
                allFields.size(), businessFields.size(), primaryKeys.size(),
                partitionFields.size(), incrementFields.size()));

//        // 添加大模型调用信息
//        header.append(String.format(LLM_TRANSLATION_INFO_FORMAT,
//                translationResult.getTranslationStatus(),
//                translationResult.getFieldsToTranslateCount()));

        header.append(String.format(GENERATE_TIME_FORMAT, LocalDateTime.now().format(FORMATTER)));
        header.append(COMMENT_SEPARATOR);
        header.append(NEWLINE);
        return header.toString();
    }

    /**
     * 构建数据加工任务的注释头
     *
     * @param targetModel 目标数据模型
     * @param sourceModel 源数据模型
     * @param businessFields 业务字段列表
     * @param primaryKeys 主键字段列表
     * @param allFields 所有字段列表
     * @param partitionFields 分区字段列表
     * @param incrementFields 增量字段列表
     * @param translationResult 字段翻译结果
     * @return {@link String }
     */

    private String buildDataGovernanceCommentHeader(DataModel targetModel, DataModel sourceModel,
            List<DataModelField> businessFields, List<DataModelField> primaryKeys,
            List<DataModelField> allFields, List<DataModelField> partitionFields,
            List<DataModelField> incrementFields, FieldTranslationResult translationResult) {
        StringBuilder header = new StringBuilder();
        header.append(COMMENT_SEPARATOR);
        header.append(DATA_GOVERNANCE_COMMENT_TITLE);
        header.append(COMMENT_SEPARATOR);
        header.append(String.format(TARGET_TABLE_FORMAT, targetModel.getZhName(), targetModel.getEnName()));
        header.append(String.format(SOURCE_TABLE_FORMAT, sourceModel.getZhName(), sourceModel.getEnName()));
//        header.append(String.format(MODEL_DESCRIPTION_FORMAT,
//                StringUtils.defaultIfBlank(targetModel.getDescription(), NO_DESCRIPTION)));
        header.append(String.format(BUSINESS_FIELDS_COUNT_FORMAT, businessFields.size()));
        header.append(String.format(PRIMARY_KEYS_COUNT_FORMAT, primaryKeys.size()));

        // 添加字段分类统计信息
        header.append(String.format(FIELDS_STATISTICS_FORMAT,
                allFields.size(), businessFields.size(), primaryKeys.size(),
                partitionFields.size(), incrementFields.size()));

//        // 添加大模型调用信息
//        header.append(String.format(LLM_TRANSLATION_INFO_FORMAT,
//                translationResult.getTranslationStatus(),
//                translationResult.getFieldsToTranslateCount()));

        header.append(String.format(GENERATE_TIME_FORMAT, LocalDateTime.now().format(FORMATTER)));
        header.append(COMMENT_SEPARATOR);
        header.append(NEWLINE);
        return header.toString();
    }

    /**
     * 生成主键MD5表达式
     *
     * @param primaryKeys 主键字段列表
     * @return {@link String }
     */
    private String generateprimaryKeyMd5(List<DataModelField> primaryKeys) {
        if (primaryKeys.isEmpty()) {
            // 如果没有指定主键，使用 id 字段
            return MD5_DEFAULT_KEY_FORMAT;
        }

        if (primaryKeys.size() == 1) {
            DataModelField primaryKey = primaryKeys.get(0);
            return String.format(MD5_SINGLE_KEY_FORMAT, primaryKey.getEnName());
        } else {
            // 多个主键字段
            String keyFields = primaryKeys.stream()
                    .map(field -> String.format(CAST_FORMAT, field.getEnName()))
                    .collect(Collectors.joining(COMMA_SEPARATOR));
            return String.format(MD5_COMPOSITE_KEY_FORMAT, keyFields);
        }
    }
}
