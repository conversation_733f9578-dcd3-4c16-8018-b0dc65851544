package com.trs.ai.moye.data.model.service.impl;

import com.trs.ai.moye.data.model.dao.DataModelDisplayMapper;
import com.trs.ai.moye.data.model.entity.MetadataRelation;
import com.trs.ai.moye.data.model.enums.CategoryTreeNodeType;
import com.trs.ai.moye.data.model.request.BusinessCategoryRequest;
import com.trs.ai.moye.data.model.request.CategoryOrderSaveRequest;
import com.trs.ai.moye.data.model.request.CategoryTreeRequest;
import com.trs.ai.moye.data.model.request.DataModelSearchRequest;
import com.trs.ai.moye.data.model.response.CategoryTreeResponse;
import com.trs.ai.moye.data.model.response.DataModelTreeResponse;
import com.trs.ai.moye.data.model.response.DataSourceConnectionResponse;
import com.trs.ai.moye.data.model.response.DataSourceConnectionResponse.ConnectionTypeResponse;
import com.trs.ai.moye.data.model.response.LayerCategoryTreeResponse;
import com.trs.ai.moye.data.model.response.ModelBasicInfoResponse;
import com.trs.ai.moye.data.model.service.DataModelCategoryService;
import com.trs.ai.moye.data.model.service.DataModelExecuteStatusService;
import com.trs.ai.moye.data.standard.response.DataStandardCategoryTreeResponse;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.connection.enums.DataSourceCategory;
import com.trs.moye.base.data.model.dao.BusinessCategoryMapper;
import com.trs.moye.base.data.model.dao.CategoryOrderConfigMapper;
import com.trs.moye.base.data.model.entity.BusinessCategory;
import com.trs.moye.base.data.model.entity.CategoryOrderConfig;
import com.trs.moye.base.data.model.entity.CategoryOrderDetail;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.enums.CategoryOrderType;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 数据模型分类服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/12 10:31
 **/
@Service
@Slf4j
public class DataModelCategoryServiceImpl implements DataModelCategoryService {

    private final Map<Integer, ConnectionType> connTypeMap = new HashMap<>();
    private Map<ConnectionType, List<DataConnection>> connections;
    public static final String MSG_ZH_NAME_EXIST = "中文名 \"%s\" 已经存在!";
    public static final String MSG_EN_NAME_EXIST = "英文名 \"%s\" 已经存在!";

    @Resource
    private DataConnectionMapper dataConnectionMapper;

    @Resource
    private BusinessCategoryMapper businessCategoryMapper;

    @Resource
    private CategoryOrderConfigMapper categoryOrderConfigMapper;


    @Resource
    private DataModelDisplayMapper dataModelDisplayMapper;
    @Resource
    private DataModelExecuteStatusService dataModelExecuteStatusService;

    @Override
    public List<CategoryTreeResponse> getCategoryTree(CategoryTreeRequest request) {
        //获取一级分类，过滤的时候也需要全部展示，检索的时候由前端控制
        List<BusinessCategory> businessCategories = businessCategoryMapper.getAllCategory();
        List<MetadataRelation> metadataRelations = dataModelDisplayMapper.selectAllDataModelRelation(request);
        return buildCategoryTree(businessCategories, metadataRelations, request);
    }

    /**
     * 构建分类树
     *
     * @param businessCategories 业务分类
     * @param modelRelations     数据建模
     * @param request            查询数据建模目录树请求
     * @return {@link CategoryTreeResponse}
     */
    public List<CategoryTreeResponse> buildCategoryTree(List<BusinessCategory> businessCategories,
        List<MetadataRelation> modelRelations, CategoryTreeRequest request) {

        dataConnectionMapper.selectAllConnection()
            .forEach(conn -> connTypeMap.put(conn.getId(), conn.getConnectionType()));
        Map<Integer, CategoryOrderConfig> dataModelOrderConfigMap = getDataModelOrderConfigMap();
        Map<Integer, Integer> businessOrderMap = getBusinessCategoryOrderMap();
        sortBusinessCategories(businessCategories, businessOrderMap);
        // 正常封装出原始数据
        List<CategoryTreeResponse> categoryTreeResponseList = new ArrayList<>();
        for (BusinessCategory businessCategory : businessCategories) {
            CategoryTreeResponse categoryTreeResponse = new CategoryTreeResponse(businessCategory);
            categoryTreeResponse.setOrder(businessOrderMap.get(businessCategory.getId()));

            List<DataModelTreeResponse> dataModelTreeResponses = buildDataModelTreeResponses(modelRelations,
                businessCategory.getId(), request.getHasUnreadError(),
                dataModelOrderConfigMap.get(businessCategory.getId()));

            List<LayerCategoryTreeResponse> modelLayerCategory = buildLayerCategoryTree(businessCategory.getId(),
                dataModelTreeResponses);

            categoryTreeResponse.setChildren(modelLayerCategory);
            categoryTreeResponseList.add(categoryTreeResponse);
        }

        String search = request.getSearch();
        boolean hasSearch = StringUtils.isNotBlank(search);
        boolean hasOtherFilter = request.getEnableStatus() != null
            || request.getCreateTableStatus() != null
            || request.getArrangeStatus() != null
            || request.getConnectionType() != null
            || request.getModelLayerType() != null
            || request.getHasUnreadError() != null;
        String searchLower = hasSearch ? search.toLowerCase() : null;
        if (!hasOtherFilter && !hasSearch) {
            return categoryTreeResponseList;
        }
        // 初步过滤后清理节点
        categoryTreeResponseList = cleanEmptyChildren(categoryTreeResponseList);

        // search 过滤
        if (hasSearch) {
            categoryTreeResponseList = applySearchFilter(categoryTreeResponseList, searchLower);
            // search 过滤后再次清理节点
            categoryTreeResponseList = cleanEmptyChildren(categoryTreeResponseList);
        }

        return categoryTreeResponseList;
    }

    /**
     * 递归清理空 children 的节点
     *
     * @param nodes 节点列表
     * @return 清理后的节点列表
     */
    private List<CategoryTreeResponse> cleanEmptyChildren(List<CategoryTreeResponse> nodes) {
        return nodes.stream()
            .peek(node -> {
                if (node.getChildren() != null) {
                    List<?> children = node.getChildren();
                    if (children.stream().anyMatch(child -> child instanceof LayerCategoryTreeResponse)) {
                        List<LayerCategoryTreeResponse> layerChildren = children.stream()
                            .filter(LayerCategoryTreeResponse.class::isInstance)
                            .map(LayerCategoryTreeResponse.class::cast)
                            .collect(Collectors.toList());
                        // 递归清理第二层节点
                        layerChildren = layerChildren.stream()
                            .filter(layer -> {
                                if (layer.getChildren() != null) {
                                    List<?> dataModelChildren = layer.getChildren();
                                    // 移除第三层空节点
                                    List<DataModelTreeResponse> filteredDataModels = dataModelChildren.stream()
                                        .filter(DataModelTreeResponse.class::isInstance)
                                        .map(DataModelTreeResponse.class::cast)
                                        .collect(Collectors.toList());
                                    layer.setChildren(filteredDataModels);
                                    return !filteredDataModels.isEmpty();
                                }
                                return false;
                            })
                            .collect(Collectors.toList());
                        node.setChildren(layerChildren);
                    }
                }
            })
            .filter(node -> {
                if (node.getChildren() != null) {
                    List<?> children = node.getChildren();
                    return !children.isEmpty();
                }
                return false;
            })
            .collect(Collectors.toList());
    }


    /**
     * 应用 search 过滤
     *
     * @param categoryTreeResponseList 分类树响应列表
     * @param searchLower              小写搜索关键词
     * @return 过滤后的列表
     */
    private List<CategoryTreeResponse> applySearchFilter(List<CategoryTreeResponse> categoryTreeResponseList,
        String searchLower) {
        return categoryTreeResponseList.stream()
            .peek(category -> {
                // 修正类型转换
                boolean matchFirst = matchCategory(category.getName(), category.getEnName(), searchLower);
                if (matchFirst) {
                    // 第一层匹配，保留所有子节点
                    return;
                }
                List<LayerCategoryTreeResponse> filteredLayers = new ArrayList<>();
                for (Object child : category.getChildren()) {
                    if (child instanceof LayerCategoryTreeResponse layer) {
                        boolean matchSecond = matchLayer(layer, searchLower);
                        if (matchSecond) {
                            // 第二层匹配，保留所有子节点
                            filteredLayers.add(layer);
                        } else {
                            // 只保留第三层匹配 search 的数据模型
                            List<DataModelTreeResponse> filteredChildren = layer.getChildren().stream()
                                .filter(DataModelTreeResponse.class::isInstance)
                                .map(DataModelTreeResponse.class::cast)
                                .filter(model -> matchModel(model, searchLower))
                                .toList();
                            if (!filteredChildren.isEmpty()) {
                                layer.setChildren(filteredChildren);
                                filteredLayers.add(layer);
                            }
                        }
                    }
                }
                category.setChildren(filteredLayers);
            })
            .filter(category -> category.getChildren() != null && !category.getChildren().isEmpty())
            .collect(Collectors.toList());
    }

    private boolean matchCategory(String name, String enName, String searchLower) {
        return (name != null && name.toLowerCase().contains(searchLower))
            || (enName != null && enName.toLowerCase().contains(searchLower));
    }

    private boolean matchLayer(LayerCategoryTreeResponse layer, String searchLower) {
        // 对ModelLayer的原始名称（如DWD）和label（如要素库）都进行匹配
        return layer.getModelLayer().name().toLowerCase().contains(searchLower)
            || layer.getModelLayer().getLabel().toLowerCase().contains(searchLower);
    }

    private boolean matchModel(DataModelTreeResponse model, String searchLower) {
        return (model.getName() != null && model.getName().toLowerCase().contains(searchLower))
            || (model.getEnName() != null && model.getEnName().toLowerCase().contains(searchLower));
    }

    /**
     * 获取业务分类排序映射
     *
     * @return {@link Map }<{@link Integer }, {@link Integer }>
     * <AUTHOR>
     * @since 2025/06/19 17:11:00
     */
    @Override
    public Map<Integer, Integer> getBusinessCategoryOrderMap() {
        CategoryOrderConfig businessCategoryOrderConfig = categoryOrderConfigMapper.selectByTypeAndCategoryId(
            CategoryOrderType.BUSINESS_CATEGORY, null);
        if (Objects.nonNull(businessCategoryOrderConfig) && ObjectUtils.isNotEmpty(
            businessCategoryOrderConfig.getConfig())) {
            return businessCategoryOrderConfig.getConfig().stream()
                .collect(Collectors.toMap(CategoryOrderDetail::getId, CategoryOrderDetail::getOrder));
        }
        return Collections.emptyMap();
    }

    /**
     * 排序业务分类
     *
     * @param businessCategories 业务类别
     * @param businessOrderMap   业务订单图
     * <AUTHOR>
     * @since 2025/06/19 18:05:48
     */
    private void sortBusinessCategories(List<BusinessCategory> businessCategories,
        Map<Integer, Integer> businessOrderMap) {
        businessCategories.sort(
            Comparator.comparing(cat -> businessOrderMap.getOrDefault(cat.getId(), Integer.MAX_VALUE)));
    }

    /**
     * 获取数据建模顺序配置映射
     *
     * @return {@link Map }<{@link Integer }, {@link CategoryOrderConfig }>
     * <AUTHOR>
     * @since 2025/06/19 17:11:03
     */
    private Map<Integer, CategoryOrderConfig> getDataModelOrderConfigMap() {
        return categoryOrderConfigMapper.selectByType(CategoryOrderType.DATA_MODEL).stream()
            .filter(config -> Objects.nonNull(config.getCategoryId()))
            .collect(Collectors.toMap(CategoryOrderConfig::getCategoryId, config -> config, (c1, c2) -> c1));
    }

    /**
     * 构建数据建模树响应
     *
     * @param modelRelations       模型关系
     * @param categoryId           类别id
     * @param hasUnreadError       有未读错误
     * @param dataModelOrderConfig 数据模型订单配置
     * @return {@link List }<{@link DataModelTreeResponse }>
     * <AUTHOR>
     * @since 2025/06/19 17:11:06
     */
    private List<DataModelTreeResponse> buildDataModelTreeResponses(List<MetadataRelation> modelRelations,
        Integer categoryId, Boolean hasUnreadError, CategoryOrderConfig dataModelOrderConfig) {
        List<DataModelTreeResponse> dataModelTreeResponses = modelRelations.stream()
            .filter(m -> m.getCategoryId().equals(categoryId)).map(this::toDataModelTreeResponse)
            .filter(m -> hasUnreadError == null || hasUnreadError == (m.getUnreadErrorCount() > 0))
            .toList();

        return sortDataModelTreeResponses(dataModelTreeResponses, dataModelOrderConfig);
    }

    /**
     * 数据建模树响应
     *
     * @param m m
     * @return {@link DataModelTreeResponse}
     * <AUTHOR>
     * @since 2025/06/19 17:11:09
     */
    private DataModelTreeResponse toDataModelTreeResponse(MetadataRelation m) {
        DataModelTreeResponse dataModelTreeResponse = new DataModelTreeResponse();
        dataModelTreeResponse.setRecentUpdateTime(m.getRecentUpdateTime());
        dataModelTreeResponse.setId(m.getDataModelId());
        dataModelTreeResponse.setName(m.getDataModelName());
        dataModelTreeResponse.setEnName(m.getDataModelEnName());
        dataModelTreeResponse.setPid(m.getCategoryId());
        dataModelTreeResponse.setStartStatus((m.getStatus()));
        dataModelTreeResponse.setDataModelType(m.getDataModelType());
        dataModelTreeResponse.setIsMcpPublished(m.getIsMcpPublished());
        ConnectionType connectionType = connTypeMap.get(m.getConnectionId());
        if (Objects.nonNull(connectionType)) {
            dataModelTreeResponse.setDataSourceType(connectionType);//mysql
            dataModelTreeResponse.setDataSourceCategory(connectionType.getCategory());//MQ,db
        }
        dataModelTreeResponse.setDispatchType(m.getExecuteMode());
        dataModelTreeResponse.setNodeType(CategoryTreeNodeType.MODEL);
        dataModelTreeResponse.setModelLayer(m.getModelLayer());
        if (Objects.nonNull(m.getExecuteMode())) {
            dataModelTreeResponse.setUnreadErrorCount(
                dataModelExecuteStatusService.getUnreadErrorCountFromCache(m.getDataModelId(), m.getModelLayer(),
                    m.getExecuteMode()));
        }
        return dataModelTreeResponse;
    }

    /**
     * 排序数据建模树响应
     *
     * @param dataModelTreeResponses 数据模型树响应
     * @param dataModelOrderConfig   数据模型订单配置
     * @return {@link List }<{@link DataModelTreeResponse }>
     * <AUTHOR>
     * @since 2025/06/19 17:11:12
     */
    private List<DataModelTreeResponse> sortDataModelTreeResponses(List<DataModelTreeResponse> dataModelTreeResponses,
        CategoryOrderConfig dataModelOrderConfig) {
        if (CollectionUtils.isEmpty(dataModelTreeResponses)) {
            return dataModelTreeResponses;
        }

        Comparator<DataModelTreeResponse> comparator;
        if (Objects.nonNull(dataModelOrderConfig) && ObjectUtils.isNotEmpty(dataModelOrderConfig.getConfig())) {
            Map<Integer, Integer> orderMap = dataModelOrderConfig.getConfig().stream()
                .collect(Collectors.toMap(CategoryOrderDetail::getId, CategoryOrderDetail::getOrder));
            dataModelTreeResponses.forEach(resp -> resp.setOrder(orderMap.get(resp.getId())));
            comparator = Comparator.comparing(
                    (DataModelTreeResponse resp) -> orderMap.getOrDefault(resp.getId(), Integer.MAX_VALUE))
                .thenComparing(Comparator.comparing(DataModelTreeResponse::getRecentUpdateTime).reversed());
        } else {
            comparator = Comparator.comparing(DataModelTreeResponse::getRecentUpdateTime).reversed();
        }
        return dataModelTreeResponses.stream().sorted(comparator).toList();
    }

    /**
     * 构建层类别树
     *
     * @param businessCategoryId 业务类别id
     * @param dataModels         数据模型
     * @return {@link List }<{@link LayerCategoryTreeResponse }>
     * <AUTHOR>
     * @since 2025/06/19 17:11:15
     */
    private List<LayerCategoryTreeResponse> buildLayerCategoryTree(Integer businessCategoryId,
        List<DataModelTreeResponse> dataModels) {
        Map<ModelLayer, List<DataModelTreeResponse>> dataModelsByLayer = dataModels.stream()
            .filter(model -> model.getModelLayer() != null) // 过滤 null 值，数据保护
            .collect(Collectors.groupingBy(DataModelTreeResponse::getModelLayer));

        return Arrays.stream(ModelLayer.values()).map(modelLayer -> {
            LayerCategoryTreeResponse layerNode = new LayerCategoryTreeResponse(modelLayer, businessCategoryId);
            layerNode.setChildren(dataModelsByLayer.getOrDefault(modelLayer, Collections.emptyList()));
            return layerNode;
        }).toList();
    }

    @Override
    public List<DataSourceConnectionResponse> getDataSourceConnectionTree() {
        connections = dataConnectionMapper.selectAllConnection().stream()
            .collect(Collectors.groupingBy(DataConnection::getConnectionType));
        return Arrays.stream(DataSourceCategory.values()).map(this::createTreeResponse).toList();
    }

    @Override
    public Boolean addCategory(BusinessCategoryRequest request) throws BizException {
        checkRequest(request);
        return businessCategoryMapper.insert(BusinessCategoryRequest.fromAdd(request)) > 0;
    }

    private void checkRequest(BusinessCategoryRequest request) {
        // 检查中文名是否已存在
        String businessName = request.getBusinessName();
        if (Boolean.TRUE.equals(businessCategoryMapper.existByZhName(businessName))) {
            throw new BizException(MSG_ZH_NAME_EXIST, businessName);
        }
        // 检查英文名是否已存在
        String businessEnName = request.getBusinessEnName();
        if (Boolean.TRUE.equals(businessCategoryMapper.existByEnName(businessEnName))) {
            throw new BizException(MSG_EN_NAME_EXIST, businessEnName);
        }
    }

    @Override
    public Integer addCategoryReturnId(BusinessCategoryRequest request) throws BizException {
        // 检查中文名是否已存在
        checkRequest(request);
        BusinessCategory businessCategory = BusinessCategoryRequest.fromAdd(request);
        businessCategoryMapper.insert(businessCategory);
        return businessCategory.getId();
    }

    @Override
    public Boolean updateCategory(BusinessCategoryRequest request, Integer id) {
        // 检查中文名是否已存在
        String businessName = request.getBusinessName();
        if (Boolean.TRUE.equals(businessCategoryMapper.findByZhNameExceptId(businessName, id))) {
            throw new BizException(MSG_ZH_NAME_EXIST, businessName);
        }
        // 检查英文名是否已存在
        String businessEnName = request.getBusinessEnName();
        if (StringUtils.isNotBlank(businessEnName) && Boolean.TRUE.equals(
            businessCategoryMapper.findByEnNameExceptId(businessEnName, id))) {
            throw new BizException(MSG_EN_NAME_EXIST, businessEnName);
        }
        return businessCategoryMapper.updateById(BusinessCategoryRequest.fromUpdate(request, id)) > 0;
    }

    private DataSourceConnectionResponse createTreeResponse(DataSourceCategory category) {
        DataSourceConnectionResponse response = new DataSourceConnectionResponse();
        response.setValue(category.name());
        response.setLabel(category.getLabel());
        response.setConnectionTypes(getConnectionTypes(category));
        return response;
    }

    private List<ConnectionTypeResponse> getConnectionTypes(DataSourceCategory dataSourceCategory) {
        return Arrays.stream(ConnectionType.values())
            .filter(connectionType -> connectionType.getCategory().equals(dataSourceCategory))
            .map(this::createConnectionTypeResponse).toList();
    }

    private ConnectionTypeResponse createConnectionTypeResponse(ConnectionType connectionType) {
        ConnectionTypeResponse response = new ConnectionTypeResponse();
        response.setValue(connectionType.name());
        response.setLabel(connectionType.getLabel());
        response.setConnections(getConnections(connectionType));
        return response;
    }

    private List<DataConnection> getConnections(ConnectionType connectionType) {
        return connections.getOrDefault(connectionType, Collections.emptyList());
    }


    @Override
    public List<ModelBasicInfoResponse> getModelBasicInfoList(DataModelSearchRequest request) {
        List<DataModel> dataModels = dataModelDisplayMapper.selectDataModelList(request);
        if (ObjectUtils.isEmpty(dataModels)) {
            return Collections.emptyList();
        }
        return dataModels.stream().map(ModelBasicInfoResponse::new).toList();
    }

    @Override
    public void saveCategoryOrderConfig(CategoryOrderSaveRequest request) {
        CategoryOrderConfig one = categoryOrderConfigMapper.selectByTypeAndCategoryId(request.getType(),
            request.getCategoryId());
        CategoryOrderConfig categoryOrderConfig = new CategoryOrderConfig(request.getType(), request.getCategoryId(),
            request.getConfig());
        if (CategoryOrderType.DATA_MODEL.equals(request.getType()) && Objects.isNull(request.getCategoryId())) {
            throw new BizException("数据建模的排序配置必须指定分类id");
        }

        if (Objects.nonNull(one)) {
            // 更新配置
            categoryOrderConfig.setId(one.getId());
            categoryOrderConfigMapper.updateById(categoryOrderConfig);
        } else {
            categoryOrderConfigMapper.insert(categoryOrderConfig);
        }
    }

    /**
     * 搜索过滤分类树
     *
     * @param list   分类树列表
     * @param search 搜索关键词
     * @return 过滤后的分类树列表
     */
    @Override
    public List<DataStandardCategoryTreeResponse> getSearchFilteredCategoryTree(
        List<DataStandardCategoryTreeResponse> list, String search) {
        if (search != null && !search.isEmpty()) {
            List<DataStandardCategoryTreeResponse> searchResults = new ArrayList<>();
            for (DataStandardCategoryTreeResponse root : list) {
                searchResults.addAll(root.searchTreeAsDataStandard(search));
            }
            list = searchResults;
        }
        return list;
    }
}
