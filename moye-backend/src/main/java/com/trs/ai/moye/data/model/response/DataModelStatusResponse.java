package com.trs.ai.moye.data.model.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.trs.ai.moye.data.model.enums.ArrangeDisplayType;
import com.trs.ai.moye.data.model.enums.ProcessType;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.typehandler.PolymorphismTypeHandler;
import com.trs.moye.base.data.execute.ExecuteModeEnum;
import com.trs.moye.base.data.execute.ExecuteParams;
import com.trs.moye.base.data.model.enums.ModelExecuteStatus;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-10-15 14:10
 */
@Data
@NoArgsConstructor
public class DataModelStatusResponse {

    /**
     * 执行参数：执行参数
     */
    @TableField(typeHandler = PolymorphismTypeHandler.class)
    private ExecuteParams executeParams;

    /**
     * 层级
     */
    private ModelLayer layer;

    /**
     * 执行状态
     */
    private ModelExecuteStatus executeStatus;

    /**
     * 执行模式
     */
    private ExecuteModeEnum executeMode;

    /**
     * 是否已建表
     */
    private boolean tableCreated;

    /**
     * 是否能启动
     */
    private boolean startable;

    /**
     * 治理加工方式：流处理、批处理
     */
    private ProcessType processType;

    /**
     * 建模方式：DAG CODE
     */
    private ArrangeDisplayType operationMode;

    /**
     * 是否已创建调度配置
     */
    private boolean scheduleCreated;
    /**
     * 是否已发布到MCP
     */
    private Boolean isMcpPublished;
}
