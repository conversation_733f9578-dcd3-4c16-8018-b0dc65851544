package com.trs.ai.moye.data.service.service;

import com.trs.ai.moye.common.response.TreeAddResponse;
import com.trs.ai.moye.data.connection.response.DataStorageTreeResponse;
import com.trs.ai.moye.data.service.entity.DataServiceCategoryTree;
import com.trs.ai.moye.data.service.enums.ServicePublishStatus;
import com.trs.ai.moye.data.service.request.CheckNameDataServiceRequest;
import com.trs.ai.moye.data.service.request.CodeBlockRequest;
import com.trs.ai.moye.data.service.request.DataServiceCloneRequest;
import com.trs.ai.moye.data.service.request.DataServicePreviewRequest;
import com.trs.ai.moye.data.service.request.DataServiceRequest;
import com.trs.ai.moye.data.service.response.DataServiceCategoryTreeResponse;
import com.trs.ai.moye.data.service.response.DataServiceDefaultValueResponse;
import com.trs.ai.moye.data.service.response.DataServiceResponse;
import com.trs.ai.moye.data.service.response.ability.AbilityCheckUnpublishResponse;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.response.IdNameResponse;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.servlet.http.HttpServletResponse;

/**
 * 数据服务Service
 *
 * <AUTHOR>
 * @since 2024/09/25 16:15:13
 */
public interface DataServiceService {

    /**
     * 获取分类树
     *
     * @return {@link List }<{@link DataServiceCategoryTree }>
     * <AUTHOR>
     * @since 2024/09/25 16:41:29
     */
    List<DataServiceCategoryTree> getCategoryTree();

    /**
     * 检查服务名称
     *
     * @param request 数据服务
     * @return boolean
     * <AUTHOR>
     * @since 2024/09/25 17:29:49
     */
    boolean checkServiceName(CheckNameDataServiceRequest request);

    /**
     * 添加数据服务
     *
     * @param request 请求
     * @return {@link TreeAddResponse }
     * <AUTHOR>
     * @since 2024/09/25 17:35:22
     */
    TreeAddResponse addDataService(DataServiceRequest request);

    /**
     * 更新数据服务
     *
     * @param request 请求
     * <AUTHOR>
     * @since 2024/09/27 11:31:00
     */
    void updateDataService(DataServiceRequest request);

    /**
     * 数据服务预览
     *
     * @param request 请求
     * @return {@link PageResponse}
     */
    PageResponse<Map<String, Object>> preview(DataServicePreviewRequest request);

    /**
     * 获取详细信息
     *
     * @param id id
     * @return {@link DataServiceResponse }
     * <AUTHOR>
     * @since 2024/09/27 18:15:50
     */
    DataServiceResponse getDetail(Integer id);

    /**
     * 删除数据服务
     *
     * @param id id
     * <AUTHOR>
     * @since 2024/09/29 10:45:39
     */
    void deleteDataService(List<Integer> id);

    /**
     * 发布
     *
     * @param id id
     * @return {@link Boolean }
     * <AUTHOR>
     * @since 2024/09/29 16:53:48
     */
    boolean publish(Integer id);

    /**
     * 检查是否可撤销发布
     *
     * @param id id
     * @return {@link AbilityCheckUnpublishResponse }
     * <AUTHOR>
     * @since 2024/10/09 16:11:04
     */
    AbilityCheckUnpublishResponse checkUnpublish(Integer id);

    /**
     * 撤销发布
     *
     * @param id id
     * @return boolean
     * <AUTHOR>
     * @since 2024/10/09 16:35:14
     */
    boolean unpublish(Integer id);

    /**
     * 重新发布
     *
     * @param id id
     * @return boolean
     * <AUTHOR>
     * @since 2024/10/09 18:37:11
     */
    boolean republish(Integer id);

    /**
     * 获取发布状态
     *
     * @param id id
     * @return {@link ServicePublishStatus }
     * <AUTHOR>
     * @since 2024/10/10 14:27:24
     */
    ServicePublishStatus getPublishStatus(Integer id);

    /**
     * 获取可以引用的条件列表
     *
     * @param storageId 存储id
     * @param serviceId serviceId
     * @return {@link List }<{@link IdNameResponse }>
     * <AUTHOR>
     * @since 2024/10/12 17:54:51
     */
    List<IdNameResponse> getRefConditionList(Integer storageId, Integer serviceId);

    /**
     * [数据服务]代码编辑提示，包含示例代码
     *
     * @param dbType DB 类型
     * @return {@link String }
     * @throws BizException 业务异常类
     * <AUTHOR>
     * @since 2024/10/14 10:25:17
     */
    String getTips(ConnectionType dbType) throws BizException;

    /**
     * [数据服务]代码解析
     *
     * @param request 代码块
     * @return {@link List }<{@link String }>
     * @throws BizException 业务异常类
     * <AUTHOR>
     * @since 2024/10/14 10:27:18
     */
    List<String> analyseCode(CodeBlockRequest request) throws BizException;

    /**
     * 获取存储树
     *
     * @return {@link List }<{@link DataStorageTreeResponse }>
     * <AUTHOR>
     * @since 2024/10/25 15:52:34
     */
    List<DataStorageTreeResponse> getStorageTree();

    /**
     * 导出数据预览 CSV
     *
     * @param request  请求
     * @param response 响应
     * @throws IOException io异常
     * <AUTHOR>
     * @since 2024/11/07 16:48:48
     */
    void exportDataPreviewCsv(DataServicePreviewRequest request, HttpServletResponse response) throws IOException;


    /**
     * Description
     *
     * @param ids        需要移动的数据服务的ID
     * @param businessId 业务层级ID
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/12/4 14:41
     */
    boolean moveMetaDataToCategory(Set<Integer> ids, Integer businessId);

    /**
     * 获取默认值
     *
     * @param categoryId  分类ID
     * @param dataModelId 数据模型ID
     * @return {@link DataServiceDefaultValueResponse}
     */
    DataServiceDefaultValueResponse getDefaultValue(Integer categoryId, Integer dataModelId);

    /**
     * 搜索过滤数据
     *
     * @param dataServiceCategoryTreeResponses 数据服务分类树响应
     * @param search                           搜索
     * @return {@link List }<{@link DataServiceCategoryTreeResponse }>
     */
    List<DataServiceCategoryTreeResponse> searchFilterData(
        List<DataServiceCategoryTreeResponse> dataServiceCategoryTreeResponses, String search);

    /**
     * 克隆数据服务
     *
     * @param request 请求
     * @return {@link TreeAddResponse }
     */
    TreeAddResponse cloneDataService(DataServiceCloneRequest request);
}
