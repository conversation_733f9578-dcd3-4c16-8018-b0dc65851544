import com.trs.moye.base.common.enums.FieldType;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试 mergeCalculationResults 方法的默认值设置功能
 * 
 * 这个测试文件用于验证优化后的 mergeCalculationResults 方法
 * 能够正确地为缺失的字段设置默认值
 */
public class TestMergeCalculationResults {
    
    /**
     * 测试默认值设置功能
     */
    public static void testDefaultValueSetting() {
        // 模拟字段类型映射
        Map<String, FieldType> fieldTypeMap = new HashMap<>();
        fieldTypeMap.put("intField", FieldType.INT);
        fieldTypeMap.put("longField", FieldType.LONG);
        fieldTypeMap.put("floatField", FieldType.FLOAT);
        fieldTypeMap.put("doubleField", FieldType.DOUBLE);
        fieldTypeMap.put("shortField", FieldType.SHORT);
        fieldTypeMap.put("decimalField", FieldType.DECIMAL);
        fieldTypeMap.put("stringField", FieldType.STRING);
        fieldTypeMap.put("booleanField", FieldType.BOOLEAN);
        fieldTypeMap.put("dateField", FieldType.DATE);
        
        // 验证默认值
        System.out.println("=== 测试默认值设置 ===");
        
        // 数字类型默认值
        Object intDefault = getDefaultValueByFieldType(FieldType.INT);
        System.out.println("INT 默认值: " + intDefault + " (期望: 0)");
        
        Object longDefault = getDefaultValueByFieldType(FieldType.LONG);
        System.out.println("LONG 默认值: " + longDefault + " (期望: 0L)");
        
        Object floatDefault = getDefaultValueByFieldType(FieldType.FLOAT);
        System.out.println("FLOAT 默认值: " + floatDefault + " (期望: 0.0f)");
        
        Object doubleDefault = getDefaultValueByFieldType(FieldType.DOUBLE);
        System.out.println("DOUBLE 默认值: " + doubleDefault + " (期望: 0.0)");
        
        Object shortDefault = getDefaultValueByFieldType(FieldType.SHORT);
        System.out.println("SHORT 默认值: " + shortDefault + " (期望: 0)");
        
        Object decimalDefault = getDefaultValueByFieldType(FieldType.DECIMAL);
        System.out.println("DECIMAL 默认值: " + decimalDefault + " (期望: 0)");
        
        // 字符串类型默认值
        Object stringDefault = getDefaultValueByFieldType(FieldType.STRING);
        System.out.println("STRING 默认值: '" + stringDefault + "' (期望: '')");
        
        // 布尔类型默认值
        Object booleanDefault = getDefaultValueByFieldType(FieldType.BOOLEAN);
        System.out.println("BOOLEAN 默认值: " + booleanDefault + " (期望: false)");
        
        // 其他类型默认值
        Object dateDefault = getDefaultValueByFieldType(FieldType.DATE);
        System.out.println("DATE 默认值: " + dateDefault + " (期望: null)");
        
        Object objectDefault = getDefaultValueByFieldType(FieldType.OBJECT);
        System.out.println("OBJECT 默认值: " + objectDefault + " (期望: null)");
    }
    
    /**
     * 复制 IndicatorCalculationService 中的默认值生成逻辑用于测试
     */
    private static Object getDefaultValueByFieldType(FieldType fieldType) {
        if (fieldType == null) {
            return null;
        }

        try {
            switch (fieldType) {
                case INT:
                    return 0;
                case LONG:
                    return 0L;
                case FLOAT:
                    return 0.0f;
                case DOUBLE:
                    return 0.0;
                case SHORT:
                    return (short) 0;
                case DECIMAL:
                    return BigDecimal.ZERO;
                case STRING:
                case CHAR:
                case TEXT:
                    return "";
                case BOOLEAN:
                    return false;
                case DATE:
                case DATETIME:
                case TIME:
                case OBJECT:
                case ARRAY:
                case ENTITY:
                case TAG:
                case COMPOUND:
                case GRAPHICS_TAG:
                case GRAPHICS_EDGE:
                case GEOMETRY:
                case BYTE_VECTOR:
                case FLOAT_VECTOR:
                case INT_VECTOR:
                default:
                    return null;
            }
        } catch (Exception e) {
            System.err.println("获取字段类型 " + fieldType + " 的默认值失败: " + e.getMessage());
            return null;
        }
    }
    
    public static void main(String[] args) {
        testDefaultValueSetting();
    }
}
