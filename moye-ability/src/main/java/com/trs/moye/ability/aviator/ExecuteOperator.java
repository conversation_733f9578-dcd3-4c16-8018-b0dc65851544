package com.trs.moye.ability.aviator;

import com.trs.moye.ability.entity.operator.Operator;
import com.trs.moye.base.data.service.entity.Condition;
import java.util.Arrays;
import java.util.stream.Collectors;
import lombok.Data;

/**
 * 执行算子信息
 *
 * <AUTHOR>
 * @since 2025/3/19 14:10
 */
@Data
public class ExecuteOperator {

    /**
     * 执行算子
     */
    private Operator operator;
    /**
     * 执行过滤条件脚本
     */
    private String script;
    /**
     * 过滤条件字符串
     */
    private String conditionStr;


    public ExecuteOperator(Operator operator) {
        this.operator = operator;
        this.script = AviatorService.createScript(operator.getConditions());
        this.conditionStr = operator.getConditions() != null && operator.getConditions().length > 0
            ? Arrays.stream(operator.getConditions()).map(Condition::createString).collect(Collectors.joining(" ")) : "";
    }
}
