package com.trs.moye.ability.invoker;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.jayway.jsonpath.JsonPath;
import com.trs.moye.ability.domain.HttpAbilityInfo;
import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.entity.HttpRequestConfig;
import com.trs.moye.ability.entity.HttpRequestConfig.HttpContentType;
import com.trs.moye.ability.entity.InputBind;
import com.trs.moye.ability.entity.LlmConfig;
import com.trs.moye.ability.entity.Schema;
import com.trs.moye.ability.entity.Schema.ObjectTypeSchema;
import com.trs.moye.ability.exception.AbilityInvokeException;
import com.trs.moye.base.common.utils.JsonUtils;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 大语言模型能力调用器
 */
@Slf4j
public class LlmAbilityInvoker implements AbilityInvoker {

    private final OkHttpClient okHttpClient;

    public LlmAbilityInvoker(OkHttpClient okHttpClient) {
        this.okHttpClient = okHttpClient;
    }

    @Override
    public Object invoke(Ability ability, JsonNode input, InputBind inputBind) throws Throwable {
        LlmConfig llmConfig = ability.getLlmConfig();
        HttpRequestConfig httpConfig = ability.getHttpRequestConfig();
        HttpAbilityInfo httpAbilityInfo = HttpAbilityHelper.createHttpInfo(httpConfig, input, inputBind);
        String prompt = buildPromptFromTemplate(
            ability.getOutputSchema(),
            llmConfig.getPrompt(),
            httpAbilityInfo.getJsonParams());
        RequestBody requestBody = buildLlmRequestBody(llmConfig, prompt);
        Headers requestHeader = Headers.of(httpAbilityInfo.getHeaders());
        // 构建请求
        Request request = new Request.Builder()
            .url(httpConfig.getUrl())
            .method("POST", requestBody)
            .headers(requestHeader)
            .build();

        // 发送请求并处理响应
        try (Response response = okHttpClient.newCall(request).execute()) {
            String responseStr = Objects.requireNonNull(response.body()).string();
            if (response.isSuccessful()) {
                return parseResponse(responseStr);
            } else {
                throw new AbilityInvokeException("请求 LLM 失败: " + response.code() + " " + responseStr);
            }
        } catch (Exception e) {
            throw new AbilityInvokeException("请求 LLM 失败：", e);
        }
    }

    private static JsonNode parseResponse(String responseStr) {
        try {
            // 尝试将内容解析为JsonNode，去掉think
            String jsonContent = JsonPath.read(responseStr, "$.choices[0].message.content");
            jsonContent = jsonContent
                .replaceAll("(?s)<think>.*</think>", "")
                .replace("```json", "")
                .replace("```", "")
                .replace("\\\"", "\"")
                .trim();
            return JsonUtils.toJsonNode(jsonContent);
        } catch (Exception e) {
            log.error("请求结果解析失败，未能从 LLM 获取有效响应或响应格式不正确。{}", responseStr);
            throw new AbilityInvokeException(
                "请求结果解析失败，未能从 LLM 获取有效响应或响应格式不正确。" + responseStr);
        }
    }

    private String buildPromptFromTemplate(Schema schema, String promptTemplate, JsonNode jsonParams) {
        String resolvedPrompt = replacePlaceholders(promptTemplate, jsonParams);
        if (schema instanceof ObjectTypeSchema) {
            ObjectTypeSchema outputSchema = (ObjectTypeSchema) schema;
            StringBuilder outputSchemaPrompt = new StringBuilder("请根据以下输出格式解析结果：\n");
            Map<String, String> outputSchemaNode = new HashMap<>();
            outputSchema.getProperties().keySet().forEach(key -> outputSchemaNode.put(key, "xxx"));
            outputSchemaPrompt.append(JsonUtils.toJsonString(outputSchemaNode));
            outputSchemaPrompt.append("\n");
            resolvedPrompt += outputSchemaPrompt.toString();
        }
        return resolvedPrompt;
    }

    private RequestBody buildLlmRequestBody(LlmConfig llmConfig, String resolvedPrompt) {
        ObjectNode requestBody = JsonUtils.emptyNode();
        requestBody.put("model", llmConfig.getModel());
        requestBody.set("messages", createLlmMessagesNode(resolvedPrompt));
        if (llmConfig.getTemperature() != null) {
            requestBody.put("temperature", llmConfig.getTemperature());
        }
        if (llmConfig.getTopP() != null) {
            requestBody.put("top_p", llmConfig.getTopP());
        }
        if (llmConfig.getTopK() != null) {
            requestBody.put("top_k", llmConfig.getTopK());
        }
        if (llmConfig.getPresencePenalty() != null) {
            requestBody.put("presence_penalty", llmConfig.getPresencePenalty());
        }
        if (llmConfig.getMaxTokens() != null) {
            requestBody.put("max_tokens", llmConfig.getMaxTokens());
        }
        return RequestBody.create(MediaType.parse(HttpContentType.JSON.getValue()), requestBody.toString());
    }

    private ArrayNode createLlmMessagesNode(String resolvedPrompt) {
        ArrayNode messagesNode = JsonUtils.emptyArrayNode();
        ObjectNode userMessage = JsonUtils.emptyNode();
        userMessage.put("role", "user");
        userMessage.put("content", resolvedPrompt);
        messagesNode.add(userMessage);
        return messagesNode;
    }

    private String replacePlaceholders(String template, JsonNode data) {
        if (data.isObject()) {
            final StringBuilder sb = new StringBuilder(template);
            data.fields().forEachRemaining(entry -> {
                String placeholder = "{{" + entry.getKey() + "}}";
                int index = sb.indexOf(placeholder);
                while (index != -1) {
                    sb.replace(index, index + placeholder.length(), entry.getValue().asText());
                    index = sb.indexOf(placeholder, index + entry.getValue().asText().length());
                }
            });
            return sb.toString();
        }
        return template;
    }
}
