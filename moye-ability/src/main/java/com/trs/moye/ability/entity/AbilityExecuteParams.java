package com.trs.moye.ability.entity;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.trs.moye.ability.deserializer.InputBindDeserializer;
import lombok.Data;

/**
 * 能力执行参数
 */
@Data
public class AbilityExecuteParams {

    private Integer abilityId;
    private JsonNode input;
    @JsonDeserialize(using = InputBindDeserializer.class)
    private InputBind inputBind;
}
